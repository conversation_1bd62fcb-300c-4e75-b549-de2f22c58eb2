from utils.cve_database import CVEDatabase
from utils.cve_repo import CVERepo
import ast
# from cve_bench.unit_test_agent import main
import json
import os
from tqdm import tqdm 
import pandas as pd

def run_pipeline(row):
    try:
        cve_id = row["cve_id"]
        commit_hash = row["commit_id"]
        repo_url = row["project"]
        repo_dir = f"../mh_sf_cve_repos/{cve_id}"
        cve_desc = row["cve_description"]

        cve_repo = CVERepo(
            cve_id=cve_id,
            cve_desc=cve_desc,
            repo_url=repo_url,
            commit_hash=commit_hash,
            repo_dir=repo_dir
        )
        cve_repo.clone_and_checkout_previous_commit()
        return True

    except Exception as e:
        print(f"{row['cve_id']} failed to init repository: {e}")
        return False


# Load the JSONL file into a DataFrame
test_df = pd.read_csv("mh_sf_reposvul.csv")
failed_count = 0

# Get unique CVE IDs to avoid duplicate downloads
unique_cves = test_df.drop_duplicates(subset=['cve_id'])
progress_bar = tqdm(unique_cves.iterrows(), total=len(unique_cves), desc="Processing CVEs")

for _, row in progress_bar:
    cve_id = row["cve_id"]
    success = run_pipeline(row)
    if not success:
        failed_count += 1
    progress_bar.set_postfix(failed=failed_count)
        
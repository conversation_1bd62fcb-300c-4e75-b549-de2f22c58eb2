import pandas as pd

test_df = pd.read_json("reposvul_data/reposvul_c_cpp/test_c_cpp_repository2.jsonl", lines=True)
test_df

test_df = test_df[test_df["target"] == 1]

file_counts = (
    test_df.groupby("commit_id")["file_name"]
    .nunique()
    .reset_index()
    .rename(columns={"file_name": "num_unique_files"})
)

# Filter for commit_ids where all functions are in the same file (i.e., only one unique file)
single_file_commits = file_counts[file_counts["num_unique_files"] == 1]["commit_id"]

# Filter the original DataFrame to only include those commit_ids
filtered_df = test_df[test_df["commit_id"].isin(single_file_commits)].copy().reset_index(drop=True)
filtered_df

print("Func:")
print(filtered_df.iloc[0]["function"])


filtered_df.iloc[0]["line_numbers"]

print("Numbers:")
print(filtered_df.iloc[2]["line_numbers"][0]['line_change'])

import pandas as pd
import re

def line_changes_within_function(row):
    """
    Returns True if all line changes fall within the function range.
    """
    fn_start = row['function_numbers']['function_start']
    fn_end = row['function_numbers']['function_end']
    for change in row['line_numbers']:
        start = change['line_start']
        end = change['line_end']
        if start is None or end is None:
            return False
        if start < fn_start or end > fn_end:
            return False
    return True

# Filter the DataFrame
cleaned_df = filtered_df[filtered_df.apply(line_changes_within_function, axis=1)].copy().reset_index(drop=True)
cleaned_df

cleaned_df.loc[0]

print("Func:")
i = 100
print(cleaned_df.iloc[i]["function_numbers"])


cleaned_df.iloc[i]["line_numbers"]

commit_counts = cleaned_df['commit_id'].value_counts()
num_commits_with_multiple_rows = (commit_counts > 1).sum()
print("Number of commits with more than one row in cleaned_df:", num_commits_with_multiple_rows)

# Get commit_ids with more than one row in cleaned_df
multi_row_commit_ids = commit_counts[commit_counts > 1].index

# Filter cleaned_df to only those rows
multi_row_cleaned_df = cleaned_df[cleaned_df['commit_id'].isin(multi_row_commit_ids)].copy().reset_index(drop=True)
multi_row_cleaned_df

import pandas as pd
import math
import ast

def _parse_hunk_lines(hunk_text: str):
    lines = []
    for raw in hunk_text.splitlines():
        if raw.startswith('@@'):
            continue  # header
        if raw and raw[0] in (' ', '+', '-', '@'):
            tag, text = raw[0], raw[1:]
        else:
            tag, text = ' ', raw  # assume context if no prefix
        lines.append((tag, text))
    return lines

def _find_subsequence(haystack, needle):
    if not needle:
        return None
    n = len(needle)
    for i in range(len(haystack) - n + 1):
        ok = True
        for j, s in enumerate(needle):
            if haystack[i + j].rstrip() != s.rstrip():
                ok = False
                break
        if ok:
            return i
    return None

def _apply_single_hunk(func_text: str, hunk_text: str) -> str:
    func_lines = func_text.splitlines()
    hunk = _parse_hunk_lines(hunk_text)

    src_seq = [t for tag, t in hunk if tag in (' ', '-')]
    start = _find_subsequence(func_lines, src_seq)

    if start is None:
        ctx_seq = [t for tag, t in hunk if tag == ' ']
        start = _find_subsequence(func_lines, ctx_seq)

    if start is None:
        return func_text

    i = start
    new_segment = []
    for tag, text in hunk:
        if tag == ' ':
            new_segment.append(func_lines[i])
            i += 1
        elif tag == '-':
            i += 1
        elif tag == '+':
            new_segment.append(text)

    consumed = len(src_seq)
    func_lines = func_lines[:start] + new_segment + func_lines[start + consumed:]
    return "\n".join(func_lines)

def _coerce_line_numbers(val):
    """
    Normalize `line_numbers` into a list of hunk strings.
    Accepts: NaN/None, dict, list/tuple, numpy array, pandas Series, or string
    (possibly a stringified list of dicts).
    """
    # None/NaN
    if val is None or (isinstance(val, float) and math.isnan(val)):
        return []

    # pandas Series -> list
    if isinstance(val, pd.Series):
        val = val.tolist()

    # numpy array -> list (optional import)
    try:
        import numpy as np
        if isinstance(val, np.ndarray):
            val = val.tolist()
    except Exception:
        pass

    # dict -> wrap
    if isinstance(val, dict):
        val = [val]

    # string -> either parse list/dicts or single hunk string
    if isinstance(val, str):
        s = val.strip()
        if (s.startswith('[') and s.endswith(']')) or (s.startswith('{') and s.endswith('}')):
            try:
                parsed = ast.literal_eval(s)
                val = parsed if isinstance(parsed, (list, tuple)) else [parsed]
            except Exception:
                val = [s]
        else:
            val = [s]

    # ensure iterable list/tuple
    if not isinstance(val, (list, tuple)):
        val = [val]

    # Extract actual hunk strings
    hunks = []
    for item in val:
        if not item:
            continue
        if isinstance(item, dict):
            h = item.get('line_change')
            if h:
                hunks.append(h)
        else:
            hunks.append(str(item))
    return hunks

def _apply_all_hunks_to_function(func_text, line_numbers):
    hunks = _coerce_line_numbers(line_numbers)
    if not hunks:
        return func_text
    out = func_text
    for hunk_text in hunks:
        out = _apply_single_hunk(out, hunk_text)
    return out

# ---- Run over your DataFrame ----
multi_row_cleaned_df['function_after'] = multi_row_cleaned_df.apply(
    lambda row: _apply_all_hunks_to_function(row['function'], row['line_numbers']),
    axis=1
)


# test_function_after.py
import re
import pytest

def _normalize_line(s: str) -> str:
    return re.sub(r"\s+", " ", s.strip())

def _normalized_lineset(text: str) -> set[str]:
    return {
        _normalize_line(line)
        for line in text.splitlines()
        if _normalize_line(line)  # skip empty after normalization
    }

def _extract_added_lines_from_hunk(hunk_text: str) -> list[str]:
    """
    Parse a unified-diff hunk text and return the list of added lines
    (without the leading '+'), excluding headers and blank additions.
    """
    added = []
    for raw in hunk_text.splitlines():
        if raw.startswith("@@") or raw.startswith("+++") or raw.startswith("---"):
            continue
        if raw.startswith("+"):
            line = raw[1:]
            if line.strip():
                added.append(line)
    return added

def _collect_all_added_lines(line_numbers: list[dict]) -> list[str]:
    added = []
    for h in (line_numbers or []):
        hunk = h.get("line_change", "")
        added.extend(_extract_added_lines_from_hunk(hunk))
    return added

def test_function_after_contains_all_added_lines(df_with_function_after):
    """
    df_with_function_after must have columns:
      ['function_id', 'line_numbers', 'function_after']
    """
    for idx, row in df_with_function_after.iterrows():
        fid = row.get("function_id", f"<row {idx}>")
        fa = row.get("function_after", None)

        # Sanity checks on construction outcome
        if fa is None:
            pytest.fail(f"{fid}: missing 'function_after' value (None).")

        if isinstance(fa, BaseException):
            # Surface the original error clearly
            pytest.fail(f"{fid}: row_to_function_after failed: "
                        f"{fa.__class__.__name__}: {fa}")

        if not isinstance(fa, str):
            pytest.fail(f"{fid}: 'function_after' must be str, got {type(fa).__name__!s}: {fa!r}")

        added_lines = _collect_all_added_lines(row.get("line_numbers") or [])

        # If no lines were added by the diff for this row, the inclusion check is vacuously true.
        if not added_lines:
            continue

        fn_lineset = _normalized_lineset(fa)
        missing = [l for l in added_lines if _normalize_line(l) not in fn_lineset]

        assert not missing, (
            f"{fid}: {len(missing)} added line(s) not found in function_after: {missing}"
        )


test_function_after_contains_all_added_lines(multi_row_cleaned_df)

multi_row_cleaned_df.loc[0]

print(multi_row_cleaned_df.loc[0]['function'])

print(multi_row_cleaned_df.loc[0]['function_after'])

print("Numbers:")
print(multi_row_cleaned_df.iloc[0]["line_numbers"][0]['line_change'])

# Find commit_ids that appear with multiple different cve_ids
commit_cve_counts = multi_row_cleaned_df.groupby('commit_id')['cve_id'].nunique()
single_cve_commits = commit_cve_counts[commit_cve_counts == 1].index

# Filter to keep only rows where commit_id has a single cve_id
multi_row_cleaned_df = multi_row_cleaned_df[multi_row_cleaned_df['commit_id'].isin(single_cve_commits)].copy().reset_index(drop=True)
multi_row_cleaned_df['commit_id'].nunique()

multi_row_cleaned_df['cve_id'].nunique()

multi_row_cleaned_df.to_csv("mh_sf_reposvul.csv")


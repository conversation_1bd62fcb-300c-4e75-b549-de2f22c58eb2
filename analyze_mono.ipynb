{"cells": [{"cell_type": "code", "execution_count": 1, "id": "31f56611", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>function_id</th>\n", "      <th>function</th>\n", "      <th>target</th>\n", "      <th>function_numbers</th>\n", "      <th>line_numbers</th>\n", "      <th>commit_id</th>\n", "      <th>cve_id</th>\n", "      <th>cve_language</th>\n", "      <th>cve_description</th>\n", "      <th>cvss</th>\n", "      <th>...</th>\n", "      <th>url</th>\n", "      <th>file_path</th>\n", "      <th>file_name</th>\n", "      <th>file_language</th>\n", "      <th>file_target</th>\n", "      <th>static</th>\n", "      <th>caller</th>\n", "      <th>callee</th>\n", "      <th>caller_of_change</th>\n", "      <th>callee_of_change</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1e38da300e1e395a15048b0af1e5305bd91402f6_8</td>\n", "      <td>static ktime_t timerfd_get_remaining(struct ti...</td>\n", "      <td>1</td>\n", "      <td>{'function_start': 149, 'function_end': 159}</td>\n", "      <td>[{'line_change': '@@  -141,9 +150,10  @@ stati...</td>\n", "      <td>1e38da300e1e395a15048b0af1e5305bd91402f6</td>\n", "      <td>CVE-2017-10661</td>\n", "      <td>C</td>\n", "      <td>Race condition in fs/timerfd.c in the Linux ke...</td>\n", "      <td>7.0</td>\n", "      <td>...</td>\n", "      <td>https://github.com/torvalds/linux/raw/1e38da30...</td>\n", "      <td>files/2017_8/73</td>\n", "      <td>fs/timerfd.c</td>\n", "      <td>c</td>\n", "      <td>-1</td>\n", "      <td>{'flawfinder': [False, []], 'rats': [False, []...</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>804e49444c093fe58ec0df2ab436565e50dc147e_251</td>\n", "      <td>Kate::SwapFile *KTextEditor::DocumentPrivate::...</td>\n", "      <td>0</td>\n", "      <td>{'function_start': 5936, 'function_end': 5939}</td>\n", "      <td>[]</td>\n", "      <td>804e49444c093fe58ec0df2ab436565e50dc147e</td>\n", "      <td>CVE-2022-23853</td>\n", "      <td>C++</td>\n", "      <td>The LSP (Language Server Protocol) plugin in K...</td>\n", "      <td>7.8</td>\n", "      <td>...</td>\n", "      <td>https://github.com/KDE/ktexteditor/raw/804e494...</td>\n", "      <td>files/2022_2/575</td>\n", "      <td>src/document/katedocument.cpp</td>\n", "      <td>cpp</td>\n", "      <td>-1</td>\n", "      <td>{'flawfinder': [False, []], 'rats': [False, []...</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3bd7fa12e146c6051490d048a4acbfba974eeb04_62</td>\n", "      <td>static msgchunk_T *\\ndisp_sb_line(int row, msg...</td>\n", "      <td>0</td>\n", "      <td>{'function_start': 2945, 'function_end': 2970}</td>\n", "      <td>[]</td>\n", "      <td>3bd7fa12e146c6051490d048a4acbfba974eeb04</td>\n", "      <td>CVE-2023-5344</td>\n", "      <td>C</td>\n", "      <td>Heap-based Buffer Overflow in GitHub repositor...</td>\n", "      <td>7.5</td>\n", "      <td>...</td>\n", "      <td>https://github.com/vim/vim/raw/3bd7fa12e146c60...</td>\n", "      <td>files/2023_10/1787</td>\n", "      <td>src/message.c</td>\n", "      <td>c</td>\n", "      <td>-1</td>\n", "      <td>{'flawfinder': [False, []], 'rats': [False, []...</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>c06cfb08b88dfbe13be44a69ae2fdc3a7c902d81_47</td>\n", "      <td>void nfs_fattr_free_names(struct nfs_fattr *fa...</td>\n", "      <td>0</td>\n", "      <td>{'function_start': 131, 'function_end': 137}</td>\n", "      <td>[]</td>\n", "      <td>c06cfb08b88dfbe13be44a69ae2fdc3a7c902d81</td>\n", "      <td>CVE-2017-2647</td>\n", "      <td>C</td>\n", "      <td>The KEYS subsystem in the Linux kernel before ...</td>\n", "      <td>7.8</td>\n", "      <td>...</td>\n", "      <td>https://github.com/torvalds/linux/raw/c06cfb08...</td>\n", "      <td>files/2017_3/14</td>\n", "      <td>fs/nfs/idmap.c</td>\n", "      <td>c</td>\n", "      <td>0</td>\n", "      <td>{'flawfinder': [False, []], 'rats': [False, []...</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2398e650c58a6f4877dafce649188290f6e3b4f5_1</td>\n", "      <td>static bool can_impersonate_uid(kuid_t uid)\\n{...</td>\n", "      <td>0</td>\n", "      <td>{'function_start': 154, 'function_end': 157}</td>\n", "      <td>[]</td>\n", "      <td>2398e650c58a6f4877dafce649188290f6e3b4f5</td>\n", "      <td>CVE-2021-0695</td>\n", "      <td>C</td>\n", "      <td>In get_sock_stat of xt_qtaguid.c, there is a p...</td>\n", "      <td>5.5</td>\n", "      <td>...</td>\n", "      <td>https://github.com/aosp-mirror/kernel_common/r...</td>\n", "      <td>files/2021_10/845</td>\n", "      <td>net/netfilter/xt_qtaguid.c</td>\n", "      <td>c</td>\n", "      <td>-1</td>\n", "      <td>{'flawfinder': [False, []], 'rats': [False, []...</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23219</th>\n", "      <td>eb7946bb6248923d8c90fe9b84425fef97ae580d_108</td>\n", "      <td>GFile *\\nflatpak_dir_get_removed_dir (FlatpakD...</td>\n", "      <td>0</td>\n", "      <td>{'function_start': 2943, 'function_end': 2947}</td>\n", "      <td>[]</td>\n", "      <td>eb7946bb6248923d8c90fe9b84425fef97ae580d</td>\n", "      <td>CVE-2021-21381</td>\n", "      <td>C</td>\n", "      <td>Flatpak is a system for building, distributing...</td>\n", "      <td>8.2</td>\n", "      <td>...</td>\n", "      <td>https://github.com/flatpak/flatpak/raw/eb7946b...</td>\n", "      <td>files/2021_3/287</td>\n", "      <td>common/flatpak-dir.c</td>\n", "      <td>c</td>\n", "      <td>0</td>\n", "      <td>{'flawfinder': [False, []], 'rats': [False, []...</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23220</th>\n", "      <td>a721d5cc9ebed4cb3679a935f4eb2cb167a78527_170</td>\n", "      <td>apr_status_t h2_mplx_reprioritize(h2_mplx *m, ...</td>\n", "      <td>0</td>\n", "      <td>{'function_start': 649, 'function_end': 667}</td>\n", "      <td>[]</td>\n", "      <td>a721d5cc9ebed4cb3679a935f4eb2cb167a78527</td>\n", "      <td>CVE-2019-0197</td>\n", "      <td>C</td>\n", "      <td>A vulnerability was found in Apache HTTP Serve...</td>\n", "      <td>4.2</td>\n", "      <td>...</td>\n", "      <td>https://github.com/apache/httpd/raw/a721d5cc9e...</td>\n", "      <td>files/2019_6\\140</td>\n", "      <td>modules/http2/h2_mplx.c</td>\n", "      <td>c</td>\n", "      <td>0</td>\n", "      <td>{'flawfinder': [False, []], 'rats': [False, []...</td>\n", "      <td>{'modules.http2.h2_util.h2_iq_sort': 'void h2_...</td>\n", "      <td>{'modules.http2.h2_session.h2_session_process'...</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23221</th>\n", "      <td>c367f65d42e0d2e1ca248998175180aa9c2eacd0_368</td>\n", "      <td>BOOL update_recv(rdpUpdate* update, wStream* s...</td>\n", "      <td>1</td>\n", "      <td>{'function_start': 758, 'function_end': 835}</td>\n", "      <td>[{'line_change': '@@  -807,7 +810,8  @@ BOOL u...</td>\n", "      <td>c367f65d42e0d2e1ca248998175180aa9c2eacd0</td>\n", "      <td>CVE-2020-11049</td>\n", "      <td>C</td>\n", "      <td>In FreeRDP after 1.1 and before 2.0.0, there i...</td>\n", "      <td>2.2</td>\n", "      <td>...</td>\n", "      <td>https://github.com/FreeRDP/FreeRDP/raw/c367f65...</td>\n", "      <td>files/2020_5/200</td>\n", "      <td>libfreerdp/core/update.c</td>\n", "      <td>c</td>\n", "      <td>-1</td>\n", "      <td>{'flawfinder': [False, []], 'rats': [False, []...</td>\n", "      <td>{'libfreerdp.core.update.update_begin_paint': ...</td>\n", "      <td>{'libfreerdp.core.rdp.rdp_recv_data_pdu': 'int...</td>\n", "      <td>{'libfreerdp.core.update.update_read_synchroni...</td>\n", "      <td>{'libfreerdp.core.update.update_recv': 'BOOL u...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23222</th>\n", "      <td>336a98feb0d56b9ac54e12736b18785c27f75090_169</td>\n", "      <td>static void inbound_frame_set_mark(nghttp2_inb...</td>\n", "      <td>0</td>\n", "      <td>{'function_start': 5222, 'function_end': 5225}</td>\n", "      <td>[]</td>\n", "      <td>336a98feb0d56b9ac54e12736b18785c27f75090</td>\n", "      <td>CVE-2020-11080</td>\n", "      <td>C</td>\n", "      <td>In nghttp2 before version 1.41.0, the overly l...</td>\n", "      <td>7.5</td>\n", "      <td>...</td>\n", "      <td>https://github.com/nghttp2/nghttp2/raw/336a98f...</td>\n", "      <td>files/2020_6/350</td>\n", "      <td>lib/nghttp2_session.c</td>\n", "      <td>c</td>\n", "      <td>-1</td>\n", "      <td>{'flawfinder': [False, []], 'rats': [False, []...</td>\n", "      <td>{}</td>\n", "      <td>{'lib.nghttp2_session.inbound_frame_handle_pad...</td>\n", "      <td>{}</td>\n", "      <td>{'lib.nghttp2_session.nghttp2_session_mem_recv...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23223</th>\n", "      <td>360e95d45ac4123255a4c796db96337f332160ad_140</td>\n", "      <td>int sc_compare_oid(const struct sc_object_id *...</td>\n", "      <td>0</td>\n", "      <td>{'function_start': 234, 'function_end': 250}</td>\n", "      <td>[]</td>\n", "      <td>360e95d45ac4123255a4c796db96337f332160ad</td>\n", "      <td>CVE-2018-16420</td>\n", "      <td>C</td>\n", "      <td>Several buffer overflows when handling respons...</td>\n", "      <td>6.6</td>\n", "      <td>...</td>\n", "      <td>https://github.com/OpenSC/OpenSC/raw/360e95d45...</td>\n", "      <td>files/2018_9\\225</td>\n", "      <td>src/libopensc/sc.c</td>\n", "      <td>c</td>\n", "      <td>-1</td>\n", "      <td>{'flawfinder': [False, []], 'rats': [False, []...</td>\n", "      <td>{}</td>\n", "      <td>{'src.pkcs15init.pkcs15-lib.check_key_compatib...</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>23224 rows × 39 columns</p>\n", "</div>"], "text/plain": ["                                        function_id  \\\n", "0        1e38da300e1e395a15048b0af1e5305bd91402f6_8   \n", "1      804e49444c093fe58ec0df2ab436565e50dc147e_251   \n", "2       3bd7fa12e146c6051490d048a4acbfba974eeb04_62   \n", "3       c06cfb08b88dfbe13be44a69ae2fdc3a7c902d81_47   \n", "4        2398e650c58a6f4877dafce649188290f6e3b4f5_1   \n", "...                                             ...   \n", "23219  eb7946bb6248923d8c90fe9b84425fef97ae580d_108   \n", "23220  a721d5cc9ebed4cb3679a935f4eb2cb167a78527_170   \n", "23221  c367f65d42e0d2e1ca248998175180aa9c2eacd0_368   \n", "23222  336a98feb0d56b9ac54e12736b18785c27f75090_169   \n", "23223  360e95d45ac4123255a4c796db96337f332160ad_140   \n", "\n", "                                                function  target  \\\n", "0      static ktime_t timerfd_get_remaining(struct ti...       1   \n", "1      Kate::SwapFile *KTextEditor::DocumentPrivate::...       0   \n", "2      static msgchunk_T *\\ndisp_sb_line(int row, msg...       0   \n", "3      void nfs_fattr_free_names(struct nfs_fattr *fa...       0   \n", "4      static bool can_impersonate_uid(kuid_t uid)\\n{...       0   \n", "...                                                  ...     ...   \n", "23219  GFile *\\nflatpak_dir_get_removed_dir (FlatpakD...       0   \n", "23220  apr_status_t h2_mplx_reprioritize(h2_mplx *m, ...       0   \n", "23221  BOOL update_recv(rdpUpdate* update, wStream* s...       1   \n", "23222  static void inbound_frame_set_mark(nghttp2_inb...       0   \n", "23223  int sc_compare_oid(const struct sc_object_id *...       0   \n", "\n", "                                     function_numbers  \\\n", "0        {'function_start': 149, 'function_end': 159}   \n", "1      {'function_start': 5936, 'function_end': 5939}   \n", "2      {'function_start': 2945, 'function_end': 2970}   \n", "3        {'function_start': 131, 'function_end': 137}   \n", "4        {'function_start': 154, 'function_end': 157}   \n", "...                                               ...   \n", "23219  {'function_start': 2943, 'function_end': 2947}   \n", "23220    {'function_start': 649, 'function_end': 667}   \n", "23221    {'function_start': 758, 'function_end': 835}   \n", "23222  {'function_start': 5222, 'function_end': 5225}   \n", "23223    {'function_start': 234, 'function_end': 250}   \n", "\n", "                                            line_numbers  \\\n", "0      [{'line_change': '@@  -141,9 +150,10  @@ stati...   \n", "1                                                     []   \n", "2                                                     []   \n", "3                                                     []   \n", "4                                                     []   \n", "...                                                  ...   \n", "23219                                                 []   \n", "23220                                                 []   \n", "23221  [{'line_change': '@@  -807,7 +810,8  @@ BOOL u...   \n", "23222                                                 []   \n", "23223                                                 []   \n", "\n", "                                      commit_id          cve_id cve_language  \\\n", "0      1e38da300e1e395a15048b0af1e5305bd91402f6  CVE-2017-10661            C   \n", "1      804e49444c093fe58ec0df2ab436565e50dc147e  CVE-2022-23853          C++   \n", "2      3bd7fa12e146c6051490d048a4acbfba974eeb04   CVE-2023-5344            C   \n", "3      c06cfb08b88dfbe13be44a69ae2fdc3a7c902d81   CVE-2017-2647            C   \n", "4      2398e650c58a6f4877dafce649188290f6e3b4f5   CVE-2021-0695            C   \n", "...                                         ...             ...          ...   \n", "23219  eb7946bb6248923d8c90fe9b84425fef97ae580d  CVE-2021-21381            C   \n", "23220  a721d5cc9ebed4cb3679a935f4eb2cb167a78527   CVE-2019-0197            C   \n", "23221  c367f65d42e0d2e1ca248998175180aa9c2eacd0  CVE-2020-11049            C   \n", "23222  336a98feb0d56b9ac54e12736b18785c27f75090  CVE-2020-11080            C   \n", "23223  360e95d45ac4123255a4c796db96337f332160ad  CVE-2018-16420            C   \n", "\n", "                                         cve_description  cvss  ...  \\\n", "0      Race condition in fs/timerfd.c in the Linux ke...   7.0  ...   \n", "1      The LSP (Language Server Protocol) plugin in K...   7.8  ...   \n", "2      Heap-based Buffer Overflow in GitHub repositor...   7.5  ...   \n", "3      The KEYS subsystem in the Linux kernel before ...   7.8  ...   \n", "4      In get_sock_stat of xt_qtaguid.c, there is a p...   5.5  ...   \n", "...                                                  ...   ...  ...   \n", "23219  Flatpak is a system for building, distributing...   8.2  ...   \n", "23220  A vulnerability was found in Apache HTTP Serve...   4.2  ...   \n", "23221  In FreeRDP after 1.1 and before 2.0.0, there i...   2.2  ...   \n", "23222  In nghttp2 before version 1.41.0, the overly l...   7.5  ...   \n", "23223  Several buffer overflows when handling respons...   6.6  ...   \n", "\n", "                                                     url           file_path  \\\n", "0      https://github.com/torvalds/linux/raw/1e38da30...     files/2017_8/73   \n", "1      https://github.com/KDE/ktexteditor/raw/804e494...    files/2022_2/575   \n", "2      https://github.com/vim/vim/raw/3bd7fa12e146c60...  files/2023_10/1787   \n", "3      https://github.com/torvalds/linux/raw/c06cfb08...     files/2017_3/14   \n", "4      https://github.com/aosp-mirror/kernel_common/r...   files/2021_10/845   \n", "...                                                  ...                 ...   \n", "23219  https://github.com/flatpak/flatpak/raw/eb7946b...    files/2021_3/287   \n", "23220  https://github.com/apache/httpd/raw/a721d5cc9e...    files/2019_6\\140   \n", "23221  https://github.com/FreeRDP/FreeRDP/raw/c367f65...    files/2020_5/200   \n", "23222  https://github.com/nghttp2/nghttp2/raw/336a98f...    files/2020_6/350   \n", "23223  https://github.com/OpenSC/OpenSC/raw/360e95d45...    files/2018_9\\225   \n", "\n", "                           file_name file_language file_target  \\\n", "0                       fs/timerfd.c             c          -1   \n", "1      src/document/katedocument.cpp           cpp          -1   \n", "2                      src/message.c             c          -1   \n", "3                     fs/nfs/idmap.c             c           0   \n", "4         net/netfilter/xt_qtaguid.c             c          -1   \n", "...                              ...           ...         ...   \n", "23219           common/flatpak-dir.c             c           0   \n", "23220        modules/http2/h2_mplx.c             c           0   \n", "23221       libfreerdp/core/update.c             c          -1   \n", "23222          lib/nghttp2_session.c             c          -1   \n", "23223             src/libopensc/sc.c             c          -1   \n", "\n", "                                                  static  \\\n", "0      {'flawfinder': [False, []], 'rats': [False, []...   \n", "1      {'flawfinder': [False, []], 'rats': [False, []...   \n", "2      {'flawfinder': [<PERSON>alse, []], 'rats': [<PERSON>als<PERSON>, []...   \n", "3      {'flawfinder': [<PERSON>alse, []], 'rats': [<PERSON>als<PERSON>, []...   \n", "4      {'flawfinder': [<PERSON>alse, []], 'rats': [<PERSON>als<PERSON>, []...   \n", "...                                                  ...   \n", "23219  {'flawfinder': [<PERSON>alse, []], 'rats': [<PERSON>als<PERSON>, []...   \n", "23220  {'flawfinder': [<PERSON>alse, []], 'rats': [<PERSON>als<PERSON>, []...   \n", "23221  {'flawfinder': [<PERSON>alse, []], 'rats': [<PERSON>als<PERSON>, []...   \n", "23222  {'flawfinder': [<PERSON>alse, []], 'rats': [<PERSON>als<PERSON>, []...   \n", "23223  {'flawfinder': [<PERSON>alse, []], 'rats': [<PERSON>als<PERSON>, []...   \n", "\n", "                                                  caller  \\\n", "0                                                     {}   \n", "1                                                     {}   \n", "2                                                     {}   \n", "3                                                     {}   \n", "4                                                     {}   \n", "...                                                  ...   \n", "23219                                                 {}   \n", "23220  {'modules.http2.h2_util.h2_iq_sort': 'void h2_...   \n", "23221  {'libfreerdp.core.update.update_begin_paint': ...   \n", "23222                                                 {}   \n", "23223                                                 {}   \n", "\n", "                                                  callee  \\\n", "0                                                     {}   \n", "1                                                     {}   \n", "2                                                     {}   \n", "3                                                     {}   \n", "4                                                     {}   \n", "...                                                  ...   \n", "23219                                                 {}   \n", "23220  {'modules.http2.h2_session.h2_session_process'...   \n", "23221  {'libfreerdp.core.rdp.rdp_recv_data_pdu': 'int...   \n", "23222  {'lib.nghttp2_session.inbound_frame_handle_pad...   \n", "23223  {'src.pkcs15init.pkcs15-lib.check_key_compatib...   \n", "\n", "                                        caller_of_change  \\\n", "0                                                     {}   \n", "1                                                     {}   \n", "2                                                     {}   \n", "3                                                     {}   \n", "4                                                     {}   \n", "...                                                  ...   \n", "23219                                                 {}   \n", "23220                                                 {}   \n", "23221  {'libfreerdp.core.update.update_read_synchroni...   \n", "23222                                                 {}   \n", "23223                                                 {}   \n", "\n", "                                        callee_of_change  \n", "0                                                     {}  \n", "1                                                     {}  \n", "2                                                     {}  \n", "3                                                     {}  \n", "4                                                     {}  \n", "...                                                  ...  \n", "23219                                                 {}  \n", "23220                                                 {}  \n", "23221  {'libfreerdp.core.update.update_recv': 'BOOL u...  \n", "23222  {'lib.nghttp2_session.nghttp2_session_mem_recv...  \n", "23223                                                 {}  \n", "\n", "[23224 rows x 39 columns]"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "\n", "test_df = pd.read_json(\"reposvul_data/reposvul_c_cpp/test_c_cpp_repository2.jsonl\", lines=True)\n", "test_df"]}, {"cell_type": "code", "execution_count": 2, "id": "31ad9b9c", "metadata": {}, "outputs": [], "source": ["test_df = test_df[test_df[\"target\"] == 1]"]}, {"cell_type": "code", "execution_count": 3, "id": "79247cf0", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>function_id</th>\n", "      <th>function</th>\n", "      <th>target</th>\n", "      <th>function_numbers</th>\n", "      <th>line_numbers</th>\n", "      <th>commit_id</th>\n", "      <th>cve_id</th>\n", "      <th>cve_language</th>\n", "      <th>cve_description</th>\n", "      <th>cvss</th>\n", "      <th>...</th>\n", "      <th>url</th>\n", "      <th>file_path</th>\n", "      <th>file_name</th>\n", "      <th>file_language</th>\n", "      <th>file_target</th>\n", "      <th>static</th>\n", "      <th>caller</th>\n", "      <th>callee</th>\n", "      <th>caller_of_change</th>\n", "      <th>callee_of_change</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1e38da300e1e395a15048b0af1e5305bd91402f6_8</td>\n", "      <td>static ktime_t timerfd_get_remaining(struct ti...</td>\n", "      <td>1</td>\n", "      <td>{'function_start': 149, 'function_end': 159}</td>\n", "      <td>[{'line_change': '@@  -141,9 +150,10  @@ stati...</td>\n", "      <td>1e38da300e1e395a15048b0af1e5305bd91402f6</td>\n", "      <td>CVE-2017-10661</td>\n", "      <td>C</td>\n", "      <td>Race condition in fs/timerfd.c in the Linux ke...</td>\n", "      <td>7.0</td>\n", "      <td>...</td>\n", "      <td>https://github.com/torvalds/linux/raw/1e38da30...</td>\n", "      <td>files/2017_8/73</td>\n", "      <td>fs/timerfd.c</td>\n", "      <td>c</td>\n", "      <td>-1</td>\n", "      <td>{'flawfinder': [False, []], 'rats': [False, []...</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>06deeec77a5a689cc94b21a8a91a76e42176685d_1</td>\n", "      <td>static int\\nsmbhash(unsigned char *out, const ...</td>\n", "      <td>1</td>\n", "      <td>{'function_start': 69, 'function_end': 112}</td>\n", "      <td>[{'line_change': '@@  -69,46 +69,22  @@ str_to...</td>\n", "      <td>06deeec77a5a689cc94b21a8a91a76e42176685d</td>\n", "      <td>CVE-2016-10154</td>\n", "      <td>C</td>\n", "      <td>The smbhash function in fs/cifs/smbencrypt.c i...</td>\n", "      <td>5.5</td>\n", "      <td>...</td>\n", "      <td>https://github.com/torvalds/linux/raw/06deeec7...</td>\n", "      <td>files/2017_2/142</td>\n", "      <td>fs/cifs/smbencrypt.c</td>\n", "      <td>c</td>\n", "      <td>1</td>\n", "      <td>{'flawfinder': [True, ['/data/rdhu/other/Stati...</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>9a271a9368eaabf99e6c2046103acb33957e63b7_21</td>\n", "      <td>static int decode_cblk(Jpeg2000DecoderContext ...</td>\n", "      <td>1</td>\n", "      <td>{'function_start': 1019, 'function_end': 1071}</td>\n", "      <td>[{'line_change': '@@  -1025,6 +1030,9  @@ stat...</td>\n", "      <td>9a271a9368eaabf99e6c2046103acb33957e63b7</td>\n", "      <td>CVE-2013-7018</td>\n", "      <td>C</td>\n", "      <td>libavcodec/jpeg2000dec.c in FFmpeg before 2.1 ...</td>\n", "      <td>5.6</td>\n", "      <td>...</td>\n", "      <td>https://github.com/FFmpeg/FFmpeg/raw/9a271a936...</td>\n", "      <td>files/2013_12/31</td>\n", "      <td>libavcodec/jpeg2000dec.c</td>\n", "      <td>c</td>\n", "      <td>1</td>\n", "      <td>{'flawfinder': [False, []], 'rats': [False, []...</td>\n", "      <td>{}</td>\n", "      <td>{'libavcodec.jpeg2000dec.jpeg2000_decode_tile'...</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4a342f01e5439b9bb901aff1c6c23c536baeeb3f_22</td>\n", "      <td>int yr_object_array_set_item(\\n    YR_OBJECT* ...</td>\n", "      <td>1</td>\n", "      <td>{'function_start': 711, 'function_end': 760}</td>\n", "      <td>[{'line_change': '@@  -725,7 +725,10  @@ int y...</td>\n", "      <td>4a342f01e5439b9bb901aff1c6c23c536baeeb3f</td>\n", "      <td>CVE-2017-11328</td>\n", "      <td>C</td>\n", "      <td>Heap buffer overflow in the yr_object_array_se...</td>\n", "      <td>5.5</td>\n", "      <td>...</td>\n", "      <td>https://github.com/VirusTotal/yara/raw/4a342f0...</td>\n", "      <td>files/2017_7/42</td>\n", "      <td>libyara/object.c</td>\n", "      <td>c</td>\n", "      <td>-1</td>\n", "      <td>{'flawfinder': [False, []], 'rats': [False, []...</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2398e650c58a6f4877dafce649188290f6e3b4f5_44</td>\n", "      <td>static int ipx_proto(const struct sk_buff *skb...</td>\n", "      <td>1</td>\n", "      <td>{'function_start': 1082, 'function_end': 1101}</td>\n", "      <td>[{'line_change': '@@  -1067,18 +1067,6  @@ sta...</td>\n", "      <td>2398e650c58a6f4877dafce649188290f6e3b4f5</td>\n", "      <td>CVE-2021-0695</td>\n", "      <td>C</td>\n", "      <td>In get_sock_stat of xt_qtaguid.c, there is a p...</td>\n", "      <td>5.5</td>\n", "      <td>...</td>\n", "      <td>https://github.com/aosp-mirror/kernel_common/r...</td>\n", "      <td>files/2021_10/845</td>\n", "      <td>net/netfilter/xt_qtaguid.c</td>\n", "      <td>c</td>\n", "      <td>-1</td>\n", "      <td>{'flawfinder': [False, []], 'rats': [False, []...</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>534</th>\n", "      <td>8f87c7c03da55f9c79bd92e67fa2c94b2a7ce5cf_6</td>\n", "      <td>int delete_sdp_line( struct sip_msg * msg, cha...</td>\n", "      <td>1</td>\n", "      <td>{'function_start': 350, 'function_end': 375}</td>\n", "      <td>[{'line_change': '@@  -347,7 +347,10  @@ stati...</td>\n", "      <td>8f87c7c03da55f9c79bd92e67fa2c94b2a7ce5cf</td>\n", "      <td>CVE-2023-27601</td>\n", "      <td>C</td>\n", "      <td>OpenSIPS is a Session Initiation Protocol (SIP...</td>\n", "      <td>7.5</td>\n", "      <td>...</td>\n", "      <td>https://github.com/OpenSIPS/opensips/raw/8f87c...</td>\n", "      <td>files/2023_3/641</td>\n", "      <td>modules/sipmsgops/codecs.c</td>\n", "      <td>c</td>\n", "      <td>-1</td>\n", "      <td>{'flawfinder': [False, []], 'rats': [False, []...</td>\n", "      <td>{}</td>\n", "      <td>{'modules.sipmsgops.codecs.stream_process': 's...</td>\n", "      <td>{}</td>\n", "      <td>{'modules.sipmsgops.codecs.stream_process': 's...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>535</th>\n", "      <td>8682ad204392b914ab1cc6ebcca9c27c19c1a4b4_33</td>\n", "      <td>void CLASS nikon_yuv_load_raw()\\n{\\n  int row,...</td>\n", "      <td>1</td>\n", "      <td>{'function_start': 1341, 'function_end': 1346}</td>\n", "      <td>[{'line_change': '@@  -1340,6 +1340,10  @@ voi...</td>\n", "      <td>8682ad204392b914ab1cc6ebcca9c27c19c1a4b4</td>\n", "      <td>CVE-2018-5802</td>\n", "      <td>C++</td>\n", "      <td>An error within the \"kodak_radc_load_raw()\" fu...</td>\n", "      <td>8.8</td>\n", "      <td>...</td>\n", "      <td>https://github.com/LibRaw/LibRaw/raw/8682ad204...</td>\n", "      <td>files/2018_12\\182</td>\n", "      <td>internal/dcraw_common.cpp</td>\n", "      <td>cpp</td>\n", "      <td>-1</td>\n", "      <td>{'flawfinder': [False, []], 'rats': [False, []...</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>536</th>\n", "      <td>8682ad204392b914ab1cc6ebcca9c27c19c1a4b4_74</td>\n", "      <td>void CLASS kodak_rgb_load_raw()\\n{\\n  short bu...</td>\n", "      <td>1</td>\n", "      <td>{'function_start': 3292, 'function_end': 3318}</td>\n", "      <td>[{'line_change': '@@  -3291,6 +3320,10  @@ voi...</td>\n", "      <td>8682ad204392b914ab1cc6ebcca9c27c19c1a4b4</td>\n", "      <td>CVE-2018-5802</td>\n", "      <td>C++</td>\n", "      <td>An error within the \"kodak_radc_load_raw()\" fu...</td>\n", "      <td>8.8</td>\n", "      <td>...</td>\n", "      <td>https://github.com/LibRaw/LibRaw/raw/8682ad204...</td>\n", "      <td>files/2018_12\\182</td>\n", "      <td>internal/dcraw_common.cpp</td>\n", "      <td>cpp</td>\n", "      <td>-1</td>\n", "      <td>{'flawfinder': [False, []], 'rats': [False, []...</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>537</th>\n", "      <td>3358f060fc182551822576b2c0a8850faab5d543_137</td>\n", "      <td>MagickPPExport void Magick::throwException(Exc...</td>\n", "      <td>1</td>\n", "      <td>{'function_start': 819, 'function_end': 977}</td>\n", "      <td>[{'line_change': '@@  -852,12 +852,18  @@ Magi...</td>\n", "      <td>3358f060fc182551822576b2c0a8850faab5d543</td>\n", "      <td>CVE-2017-6499</td>\n", "      <td>C++</td>\n", "      <td>An issue was discovered in Magick++ in ImageMa...</td>\n", "      <td>5.5</td>\n", "      <td>...</td>\n", "      <td>https://github.com/ImageMagick/ImageMagick/raw...</td>\n", "      <td>files/2017_3/898</td>\n", "      <td>Magic<PERSON>++/lib/Exception.cpp</td>\n", "      <td>cpp</td>\n", "      <td>-1</td>\n", "      <td>{'flawfinder': [False, []], 'rats': [False, []...</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>538</th>\n", "      <td>c367f65d42e0d2e1ca248998175180aa9c2eacd0_368</td>\n", "      <td>BOOL update_recv(rdpUpdate* update, wStream* s...</td>\n", "      <td>1</td>\n", "      <td>{'function_start': 758, 'function_end': 835}</td>\n", "      <td>[{'line_change': '@@  -807,7 +810,8  @@ BOOL u...</td>\n", "      <td>c367f65d42e0d2e1ca248998175180aa9c2eacd0</td>\n", "      <td>CVE-2020-11049</td>\n", "      <td>C</td>\n", "      <td>In FreeRDP after 1.1 and before 2.0.0, there i...</td>\n", "      <td>2.2</td>\n", "      <td>...</td>\n", "      <td>https://github.com/FreeRDP/FreeRDP/raw/c367f65...</td>\n", "      <td>files/2020_5/200</td>\n", "      <td>libfreerdp/core/update.c</td>\n", "      <td>c</td>\n", "      <td>-1</td>\n", "      <td>{'flawfinder': [False, []], 'rats': [False, []...</td>\n", "      <td>{'libfreerdp.core.update.update_begin_paint': ...</td>\n", "      <td>{'libfreerdp.core.rdp.rdp_recv_data_pdu': 'int...</td>\n", "      <td>{'libfreerdp.core.update.update_read_synchroni...</td>\n", "      <td>{'libfreerdp.core.update.update_recv': 'BOOL u...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>539 rows × 39 columns</p>\n", "</div>"], "text/plain": ["                                      function_id  \\\n", "0      1e38da300e1e395a15048b0af1e5305bd91402f6_8   \n", "1      06deeec77a5a689cc94b21a8a91a76e42176685d_1   \n", "2     9a271a9368eaabf99e6c2046103acb33957e63b7_21   \n", "3     4a342f01e5439b9bb901aff1c6c23c536baeeb3f_22   \n", "4     2398e650c58a6f4877dafce649188290f6e3b4f5_44   \n", "..                                            ...   \n", "534    8f87c7c03da55f9c79bd92e67fa2c94b2a7ce5cf_6   \n", "535   8682ad204392b914ab1cc6ebcca9c27c19c1a4b4_33   \n", "536   8682ad204392b914ab1cc6ebcca9c27c19c1a4b4_74   \n", "537  3358f060fc182551822576b2c0a8850faab5d543_137   \n", "538  c367f65d42e0d2e1ca248998175180aa9c2eacd0_368   \n", "\n", "                                              function  target  \\\n", "0    static ktime_t timerfd_get_remaining(struct ti...       1   \n", "1    static int\\nsmbhash(unsigned char *out, const ...       1   \n", "2    static int decode_cblk(Jpeg2000DecoderContext ...       1   \n", "3    int yr_object_array_set_item(\\n    YR_OBJECT* ...       1   \n", "4    static int ipx_proto(const struct sk_buff *skb...       1   \n", "..                                                 ...     ...   \n", "534  int delete_sdp_line( struct sip_msg * msg, cha...       1   \n", "535  void CLASS nikon_yuv_load_raw()\\n{\\n  int row,...       1   \n", "536  void CLASS kodak_rgb_load_raw()\\n{\\n  short bu...       1   \n", "537  MagickPPExport void Magick::throwException(Exc...       1   \n", "538  BOOL update_recv(rdpUpdate* update, wStream* s...       1   \n", "\n", "                                   function_numbers  \\\n", "0      {'function_start': 149, 'function_end': 159}   \n", "1       {'function_start': 69, 'function_end': 112}   \n", "2    {'function_start': 1019, 'function_end': 1071}   \n", "3      {'function_start': 711, 'function_end': 760}   \n", "4    {'function_start': 1082, 'function_end': 1101}   \n", "..                                              ...   \n", "534    {'function_start': 350, 'function_end': 375}   \n", "535  {'function_start': 1341, 'function_end': 1346}   \n", "536  {'function_start': 3292, 'function_end': 3318}   \n", "537    {'function_start': 819, 'function_end': 977}   \n", "538    {'function_start': 758, 'function_end': 835}   \n", "\n", "                                          line_numbers  \\\n", "0    [{'line_change': '@@  -141,9 +150,10  @@ stati...   \n", "1    [{'line_change': '@@  -69,46 +69,22  @@ str_to...   \n", "2    [{'line_change': '@@  -1025,6 +1030,9  @@ stat...   \n", "3    [{'line_change': '@@  -725,7 +725,10  @@ int y...   \n", "4    [{'line_change': '@@  -1067,18 +1067,6  @@ sta...   \n", "..                                                 ...   \n", "534  [{'line_change': '@@  -347,7 +347,10  @@ stati...   \n", "535  [{'line_change': '@@  -1340,6 +1340,10  @@ voi...   \n", "536  [{'line_change': '@@  -3291,6 +3320,10  @@ voi...   \n", "537  [{'line_change': '@@  -852,12 +852,18  @@ Magi...   \n", "538  [{'line_change': '@@  -807,7 +810,8  @@ BOOL u...   \n", "\n", "                                    commit_id          cve_id cve_language  \\\n", "0    1e38da300e1e395a15048b0af1e5305bd91402f6  CVE-2017-10661            C   \n", "1    06deeec77a5a689cc94b21a8a91a76e42176685d  CVE-2016-10154            C   \n", "2    9a271a9368eaabf99e6c2046103acb33957e63b7   CVE-2013-7018            C   \n", "3    4a342f01e5439b9bb901aff1c6c23c536baeeb3f  CVE-2017-11328            C   \n", "4    2398e650c58a6f4877dafce649188290f6e3b4f5   CVE-2021-0695            C   \n", "..                                        ...             ...          ...   \n", "534  8f87c7c03da55f9c79bd92e67fa2c94b2a7ce5cf  CVE-2023-27601            C   \n", "535  8682ad204392b914ab1cc6ebcca9c27c19c1a4b4   CVE-2018-5802          C++   \n", "536  8682ad204392b914ab1cc6ebcca9c27c19c1a4b4   CVE-2018-5802          C++   \n", "537  3358f060fc182551822576b2c0a8850faab5d543   CVE-2017-6499          C++   \n", "538  c367f65d42e0d2e1ca248998175180aa9c2eacd0  CVE-2020-11049            C   \n", "\n", "                                       cve_description  cvss  ...  \\\n", "0    Race condition in fs/timerfd.c in the Linux ke...   7.0  ...   \n", "1    The smbhash function in fs/cifs/smbencrypt.c i...   5.5  ...   \n", "2    libavcodec/jpeg2000dec.c in FFmpeg before 2.1 ...   5.6  ...   \n", "3    Heap buffer overflow in the yr_object_array_se...   5.5  ...   \n", "4    In get_sock_stat of xt_qtaguid.c, there is a p...   5.5  ...   \n", "..                                                 ...   ...  ...   \n", "534  OpenSIPS is a Session Initiation Protocol (SIP...   7.5  ...   \n", "535  An error within the \"kodak_radc_load_raw()\" fu...   8.8  ...   \n", "536  An error within the \"kodak_radc_load_raw()\" fu...   8.8  ...   \n", "537  An issue was discovered in Magick++ in ImageMa...   5.5  ...   \n", "538  In FreeRDP after 1.1 and before 2.0.0, there i...   2.2  ...   \n", "\n", "                                                   url          file_path  \\\n", "0    https://github.com/torvalds/linux/raw/1e38da30...    files/2017_8/73   \n", "1    https://github.com/torvalds/linux/raw/06deeec7...   files/2017_2/142   \n", "2    https://github.com/FFmpeg/FFmpeg/raw/9a271a936...   files/2013_12/31   \n", "3    https://github.com/VirusTotal/yara/raw/4a342f0...    files/2017_7/42   \n", "4    https://github.com/aosp-mirror/kernel_common/r...  files/2021_10/845   \n", "..                                                 ...                ...   \n", "534  https://github.com/OpenSIPS/opensips/raw/8f87c...   files/2023_3/641   \n", "535  https://github.com/LibRaw/LibRaw/raw/8682ad204...  files/2018_12\\182   \n", "536  https://github.com/LibRaw/LibRaw/raw/8682ad204...  files/2018_12\\182   \n", "537  https://github.com/ImageMagick/ImageMagick/raw...   files/2017_3/898   \n", "538  https://github.com/FreeRDP/FreeRDP/raw/c367f65...   files/2020_5/200   \n", "\n", "                      file_name file_language file_target  \\\n", "0                  fs/timerfd.c             c          -1   \n", "1          fs/cifs/smbencrypt.c             c           1   \n", "2      libavcodec/jpeg2000dec.c             c           1   \n", "3              libyara/object.c             c          -1   \n", "4    net/netfilter/xt_qtaguid.c             c          -1   \n", "..                          ...           ...         ...   \n", "534  modules/sipmsgops/codecs.c             c          -1   \n", "535   internal/dcraw_common.cpp           cpp          -1   \n", "536   internal/dcraw_common.cpp           cpp          -1   \n", "537  Magick++/lib/Exception.cpp           cpp          -1   \n", "538    libfreerdp/core/update.c             c          -1   \n", "\n", "                                                static  \\\n", "0    {'flawfinder': [False, []], 'rats': [False, []...   \n", "1    {'flawfinder': [True, ['/data/rdhu/other/Stati...   \n", "2    {'flawfinder': [<PERSON>alse, []], 'rats': [<PERSON>als<PERSON>, []...   \n", "3    {'flawfinder': [<PERSON>alse, []], 'rats': [<PERSON>als<PERSON>, []...   \n", "4    {'flawfinder': [<PERSON>alse, []], 'rats': [<PERSON>als<PERSON>, []...   \n", "..                                                 ...   \n", "534  {'flawfinder': [<PERSON>alse, []], 'rats': [<PERSON>als<PERSON>, []...   \n", "535  {'flawfinder': [<PERSON>alse, []], 'rats': [<PERSON>als<PERSON>, []...   \n", "536  {'flawfinder': [<PERSON>alse, []], 'rats': [<PERSON>als<PERSON>, []...   \n", "537  {'flawfinder': [<PERSON>alse, []], 'rats': [<PERSON>als<PERSON>, []...   \n", "538  {'flawfinder': [<PERSON>alse, []], 'rats': [<PERSON>als<PERSON>, []...   \n", "\n", "                                                caller  \\\n", "0                                                   {}   \n", "1                                                   {}   \n", "2                                                   {}   \n", "3                                                   {}   \n", "4                                                   {}   \n", "..                                                 ...   \n", "534                                                 {}   \n", "535                                                 {}   \n", "536                                                 {}   \n", "537                                                 {}   \n", "538  {'libfreerdp.core.update.update_begin_paint': ...   \n", "\n", "                                                callee  \\\n", "0                                                   {}   \n", "1                                                   {}   \n", "2    {'libavcodec.jpeg2000dec.jpeg2000_decode_tile'...   \n", "3                                                   {}   \n", "4                                                   {}   \n", "..                                                 ...   \n", "534  {'modules.sipmsgops.codecs.stream_process': 's...   \n", "535                                                 {}   \n", "536                                                 {}   \n", "537                                                 {}   \n", "538  {'libfreerdp.core.rdp.rdp_recv_data_pdu': 'int...   \n", "\n", "                                      caller_of_change  \\\n", "0                                                   {}   \n", "1                                                   {}   \n", "2                                                   {}   \n", "3                                                   {}   \n", "4                                                   {}   \n", "..                                                 ...   \n", "534                                                 {}   \n", "535                                                 {}   \n", "536                                                 {}   \n", "537                                                 {}   \n", "538  {'libfreerdp.core.update.update_read_synchroni...   \n", "\n", "                                      callee_of_change  \n", "0                                                   {}  \n", "1                                                   {}  \n", "2                                                   {}  \n", "3                                                   {}  \n", "4                                                   {}  \n", "..                                                 ...  \n", "534  {'modules.sipmsgops.codecs.stream_process': 's...  \n", "535                                                 {}  \n", "536                                                 {}  \n", "537                                                 {}  \n", "538  {'libfreerdp.core.update.update_recv': 'BOOL u...  \n", "\n", "[539 rows x 39 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["file_counts = (\n", "    test_df.groupby(\"commit_id\")[\"file_name\"]\n", "    .nunique()\n", "    .reset_index()\n", "    .rename(columns={\"file_name\": \"num_unique_files\"})\n", ")\n", "\n", "# Filter for commit_ids where all functions are in the same file (i.e., only one unique file)\n", "single_file_commits = file_counts[file_counts[\"num_unique_files\"] == 1][\"commit_id\"]\n", "\n", "# Filter the original DataFrame to only include those commit_ids\n", "filtered_df = test_df[test_df[\"commit_id\"].isin(single_file_commits)].copy().reset_index(drop=True)\n", "filtered_df"]}, {"cell_type": "code", "execution_count": 4, "id": "8307b398", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Func:\n", "static ktime_t timerfd_get_remaining(struct timerfd_ctx *ctx)\n", "{\n", "\tktime_t remaining;\n", "\n", "\tif (isalarm(ctx))\n", "\t\tremaining = alarm_expires_remaining(&ctx->t.alarm);\n", "\telse\n", "\t\tremaining = hrtimer_expires_remaining_adjusted(&ctx->t.tmr);\n", "\n", "\treturn remaining < 0 ? 0: remaining;\n", "}\n"]}], "source": ["print(\"Func:\")\n", "print(filtered_df.iloc[0][\"function\"])\n"]}, {"cell_type": "code", "execution_count": 5, "id": "b8f50ca7", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'line_change': '@@  -141,9 +150,10  @@ static void timerfd_setup_cancel(struct timerfd_ctx *ctx, int flags)\\n \\t\\t\\tlist_add_rcu(&ctx->clist, &cancel_list);\\n \\t\\t\\tspin_unlock(&cancel_lock);\\n \\t\\t}\\n-\\t} else if (ctx->might_cancel) {\\n-\\t\\ttimerfd_remove_cancel(ctx);\\n+\\t} else {\\n+\\t\\t__timerfd_remove_cancel(ctx);\\n \\t}\\n+\\tspin_unlock(&ctx->cancel_lock);\\n }\\n \\n static ktime_t timerfd_get_remaining(struct timerfd_ctx *ctx)\\n',\n", "  'line_start': 141,\n", "  'line_end': 149}]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["filtered_df.iloc[0][\"line_numbers\"]"]}, {"cell_type": "code", "execution_count": 6, "id": "b53ab77b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Numbers:\n", "@@  -1025,6 +1030,9  @@ static int decode_cblk(Jpeg2000DecoderContext *s, Jpeg2000CodingStyle *codsty,\n", "     int bpass_csty_symbol           = codsty->cblk_style & JPEG2000_CBLK_BYPASS;\n", "     int vert_causal_ctx_csty_symbol = codsty->cblk_style & JPEG2000_CBLK_VSC;\n", " \n", "+    av_assert0(width  <= JPEG2000_MAX_CBLKW);\n", "+    av_assert0(height <= JPEG2000_MAX_CBLKH);\n", "+\n", "     for (y = 0; y < height; y++)\n", "         memset(t1->data[y], 0, width * sizeof(**t1->data));\n", " \n"]}], "source": ["print(\"Numbers:\")\n", "print(filtered_df.iloc[2][\"line_numbers\"][0]['line_change'])"]}, {"cell_type": "code", "execution_count": 7, "id": "b3006ec9", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>function_id</th>\n", "      <th>function</th>\n", "      <th>target</th>\n", "      <th>function_numbers</th>\n", "      <th>line_numbers</th>\n", "      <th>commit_id</th>\n", "      <th>cve_id</th>\n", "      <th>cve_language</th>\n", "      <th>cve_description</th>\n", "      <th>cvss</th>\n", "      <th>...</th>\n", "      <th>url</th>\n", "      <th>file_path</th>\n", "      <th>file_name</th>\n", "      <th>file_language</th>\n", "      <th>file_target</th>\n", "      <th>static</th>\n", "      <th>caller</th>\n", "      <th>callee</th>\n", "      <th>caller_of_change</th>\n", "      <th>callee_of_change</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>9a271a9368eaabf99e6c2046103acb33957e63b7_21</td>\n", "      <td>static int decode_cblk(Jpeg2000DecoderContext ...</td>\n", "      <td>1</td>\n", "      <td>{'function_start': 1019, 'function_end': 1071}</td>\n", "      <td>[{'line_change': '@@  -1025,6 +1030,9  @@ stat...</td>\n", "      <td>9a271a9368eaabf99e6c2046103acb33957e63b7</td>\n", "      <td>CVE-2013-7018</td>\n", "      <td>C</td>\n", "      <td>libavcodec/jpeg2000dec.c in FFmpeg before 2.1 ...</td>\n", "      <td>5.6</td>\n", "      <td>...</td>\n", "      <td>https://github.com/FFmpeg/FFmpeg/raw/9a271a936...</td>\n", "      <td>files/2013_12/31</td>\n", "      <td>libavcodec/jpeg2000dec.c</td>\n", "      <td>c</td>\n", "      <td>1</td>\n", "      <td>{'flawfinder': [False, []], 'rats': [False, []...</td>\n", "      <td>{}</td>\n", "      <td>{'libavcodec.jpeg2000dec.jpeg2000_decode_tile'...</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>4a342f01e5439b9bb901aff1c6c23c536baeeb3f_22</td>\n", "      <td>int yr_object_array_set_item(\\n    YR_OBJECT* ...</td>\n", "      <td>1</td>\n", "      <td>{'function_start': 711, 'function_end': 760}</td>\n", "      <td>[{'line_change': '@@  -725,7 +725,10  @@ int y...</td>\n", "      <td>4a342f01e5439b9bb901aff1c6c23c536baeeb3f</td>\n", "      <td>CVE-2017-11328</td>\n", "      <td>C</td>\n", "      <td>Heap buffer overflow in the yr_object_array_se...</td>\n", "      <td>5.5</td>\n", "      <td>...</td>\n", "      <td>https://github.com/VirusTotal/yara/raw/4a342f0...</td>\n", "      <td>files/2017_7/42</td>\n", "      <td>libyara/object.c</td>\n", "      <td>c</td>\n", "      <td>-1</td>\n", "      <td>{'flawfinder': [False, []], 'rats': [False, []...</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>9a5467bf7b6e9e02ec9c3da4e23747c05faeaac6_118</td>\n", "      <td>static int crypto_report_cipher(struct sk_buff...</td>\n", "      <td>1</td>\n", "      <td>{'function_start': 74, 'function_end': 91}</td>\n", "      <td>[{'line_change': '@@  -75,7 +75,7  @@ static i...</td>\n", "      <td>9a5467bf7b6e9e02ec9c3da4e23747c05faeaac6</td>\n", "      <td>CVE-2013-2546</td>\n", "      <td>C</td>\n", "      <td>The report API in the crypto user configuratio...</td>\n", "      <td>9.8</td>\n", "      <td>...</td>\n", "      <td>https://github.com/torvalds/linux/raw/9a5467bf...</td>\n", "      <td>files/2013_3/34</td>\n", "      <td>crypto/crypto_user.c</td>\n", "      <td>c</td>\n", "      <td>1</td>\n", "      <td>{'flawfinder': [True, ['/data/rdhu/other/Stati...</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>438274f938e046d33cb0e1230b41da32ffe223e1_3</td>\n", "      <td>tmsize_t\\nTIFFReadEncodedStrip(TIFF* tif, uint...</td>\n", "      <td>1</td>\n", "      <td>{'function_start': 321, 'function_end': 384}</td>\n", "      <td>[{'line_change': '@@  -346,7 +346,7  @@ TIFFRe...</td>\n", "      <td>438274f938e046d33cb0e1230b41da32ffe223e1</td>\n", "      <td>CVE-2016-10266</td>\n", "      <td>C</td>\n", "      <td>LibTIFF 4.0.7 allows remote attackers to cause...</td>\n", "      <td>5.5</td>\n", "      <td>...</td>\n", "      <td>https://github.com/vadz/libtiff/raw/438274f938...</td>\n", "      <td>files/2017_3/245</td>\n", "      <td>libtiff/tif_read.c</td>\n", "      <td>c</td>\n", "      <td>-1</td>\n", "      <td>{'flawfinder': [False, []], 'rats': [False, []...</td>\n", "      <td>{'libtiff.tif_read.TIFFCheckRead': 'static int...</td>\n", "      <td>{'libtiff.tif_getimage.gtStripContig': 'static...</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>9216833a3888a4105a18e8c349f65b045ddb1079_16</td>\n", "      <td>void ArrowHead()\\n/*\\n    Jaxodraw style arrow...</td>\n", "      <td>1</td>\n", "      <td>{'function_start': 485, 'function_end': 522}</td>\n", "      <td>[{'line_change': '@@  -503,10 +522,10  @@ void...</td>\n", "      <td>9216833a3888a4105a18e8c349f65b045ddb1079</td>\n", "      <td>CVE-2019-18604</td>\n", "      <td>C</td>\n", "      <td>In axohelp.c before 1.3 in axohelp in axodraw2...</td>\n", "      <td>9.8</td>\n", "      <td>...</td>\n", "      <td>https://github.com/TeX-Live/texlive-source/raw...</td>\n", "      <td>files/2019_10\\43</td>\n", "      <td>utils/axodraw2/axodraw2-src/axohelp.c</td>\n", "      <td>c</td>\n", "      <td>-1</td>\n", "      <td>{'flawfinder': [True, ['/data/rdhu/other/Stati...</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>330</th>\n", "      <td>35ab4475a7df9b2a4bcab235e379c0c3ec543658_20</td>\n", "      <td>int mp4client_main(int argc, char **argv)\\n{\\n...</td>\n", "      <td>1</td>\n", "      <td>{'function_start': 1193, 'function_end': 2382}</td>\n", "      <td>[{'line_change': '@@  -1680,7 +1694,14  @@ int...</td>\n", "      <td>35ab4475a7df9b2a4bcab235e379c0c3ec543658</td>\n", "      <td>CVE-2018-20762</td>\n", "      <td>C</td>\n", "      <td>GPAC version 0.7.1 and earlier has a buffer ov...</td>\n", "      <td>7.8</td>\n", "      <td>...</td>\n", "      <td>https://github.com/gpac/gpac/raw/35ab4475a7df9...</td>\n", "      <td>files/2019_2\\97</td>\n", "      <td>applications/mp4client/main.c</td>\n", "      <td>c</td>\n", "      <td>1</td>\n", "      <td>{'flawfinder': [True, ['/data/rdhu/other/Stati...</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>331</th>\n", "      <td>8ac9a080bee25e67e49bd138d81c992ce7b6d899_35</td>\n", "      <td>DltReturnValue dlt_file_message(DltFile *file,...</td>\n", "      <td>1</td>\n", "      <td>{'function_start': 1713, 'function_end': 1747}</td>\n", "      <td>[{'line_change': '@@  -1718,7 +1718,7  @@ DltR...</td>\n", "      <td>8ac9a080bee25e67e49bd138d81c992ce7b6d899</td>\n", "      <td>CVE-2023-36321</td>\n", "      <td>C</td>\n", "      <td>Connected Vehicle Systems Alliance (COVESA) up...</td>\n", "      <td>7.5</td>\n", "      <td>...</td>\n", "      <td>https://github.com/micha<PERSON>-methn<PERSON>/dlt-daemon/...</td>\n", "      <td>files/2023_10/762</td>\n", "      <td>src/shared/dlt_common.c</td>\n", "      <td>c</td>\n", "      <td>-1</td>\n", "      <td>{'flawfinder': [False, []], 'rats': [False, []...</td>\n", "      <td>{'src.shared.dlt_common.dlt_vlog': 'DltReturnV...</td>\n", "      <td>{'src.core_dump_handler.dlt_cdh.main': 'int ma...</td>\n", "      <td>{'src.shared.dlt_common.dlt_vlog': 'DltReturnV...</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>332</th>\n", "      <td>9657bbe3cdce4aaa90e07d50c1c70ae52da0ba6a_31</td>\n", "      <td>static int readContigStripsIntoBuffer (TIFF* i...</td>\n", "      <td>1</td>\n", "      <td>{'function_start': 3673, 'function_end': 3705}</td>\n", "      <td>[{'line_change': '@@  -3698,7 +3698,7  @@ stat...</td>\n", "      <td>9657bbe3cdce4aaa90e07d50c1c70ae52da0ba6a</td>\n", "      <td>CVE-2016-10092</td>\n", "      <td>C</td>\n", "      <td>Heap-based buffer overflow in the readContigSt...</td>\n", "      <td>7.8</td>\n", "      <td>...</td>\n", "      <td>https://github.com/vadz/libtiff/raw/9657bbe3cd...</td>\n", "      <td>files/2017_3/1057</td>\n", "      <td>tools/tiffcrop.c</td>\n", "      <td>c</td>\n", "      <td>-1</td>\n", "      <td>{'flawfinder': [False, []], 'rats': [False, []...</td>\n", "      <td>{}</td>\n", "      <td>{'tools.tiffcrop.loadImage': 'static int\n", "loadI...</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>333</th>\n", "      <td>3358f060fc182551822576b2c0a8850faab5d543_137</td>\n", "      <td>MagickPPExport void Magick::throwException(Exc...</td>\n", "      <td>1</td>\n", "      <td>{'function_start': 819, 'function_end': 977}</td>\n", "      <td>[{'line_change': '@@  -852,12 +852,18  @@ Magi...</td>\n", "      <td>3358f060fc182551822576b2c0a8850faab5d543</td>\n", "      <td>CVE-2017-6499</td>\n", "      <td>C++</td>\n", "      <td>An issue was discovered in Magick++ in ImageMa...</td>\n", "      <td>5.5</td>\n", "      <td>...</td>\n", "      <td>https://github.com/ImageMagick/ImageMagick/raw...</td>\n", "      <td>files/2017_3/898</td>\n", "      <td>Magic<PERSON>++/lib/Exception.cpp</td>\n", "      <td>cpp</td>\n", "      <td>-1</td>\n", "      <td>{'flawfinder': [False, []], 'rats': [False, []...</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>334</th>\n", "      <td>c367f65d42e0d2e1ca248998175180aa9c2eacd0_368</td>\n", "      <td>BOOL update_recv(rdpUpdate* update, wStream* s...</td>\n", "      <td>1</td>\n", "      <td>{'function_start': 758, 'function_end': 835}</td>\n", "      <td>[{'line_change': '@@  -807,7 +810,8  @@ BOOL u...</td>\n", "      <td>c367f65d42e0d2e1ca248998175180aa9c2eacd0</td>\n", "      <td>CVE-2020-11049</td>\n", "      <td>C</td>\n", "      <td>In FreeRDP after 1.1 and before 2.0.0, there i...</td>\n", "      <td>2.2</td>\n", "      <td>...</td>\n", "      <td>https://github.com/FreeRDP/FreeRDP/raw/c367f65...</td>\n", "      <td>files/2020_5/200</td>\n", "      <td>libfreerdp/core/update.c</td>\n", "      <td>c</td>\n", "      <td>-1</td>\n", "      <td>{'flawfinder': [False, []], 'rats': [False, []...</td>\n", "      <td>{'libfreerdp.core.update.update_begin_paint': ...</td>\n", "      <td>{'libfreerdp.core.rdp.rdp_recv_data_pdu': 'int...</td>\n", "      <td>{'libfreerdp.core.update.update_read_synchroni...</td>\n", "      <td>{'libfreerdp.core.update.update_recv': 'BOOL u...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>335 rows × 39 columns</p>\n", "</div>"], "text/plain": ["                                      function_id  \\\n", "0     9a271a9368eaabf99e6c2046103acb33957e63b7_21   \n", "1     4a342f01e5439b9bb901aff1c6c23c536baeeb3f_22   \n", "2    9a5467bf7b6e9e02ec9c3da4e23747c05faeaac6_118   \n", "3      438274f938e046d33cb0e1230b41da32ffe223e1_3   \n", "4     9216833a3888a4105a18e8c349f65b045ddb1079_16   \n", "..                                            ...   \n", "330   35ab4475a7df9b2a4bcab235e379c0c3ec543658_20   \n", "331   8ac9a080bee25e67e49bd138d81c992ce7b6d899_35   \n", "332   9657bbe3cdce4aaa90e07d50c1c70ae52da0ba6a_31   \n", "333  3358f060fc182551822576b2c0a8850faab5d543_137   \n", "334  c367f65d42e0d2e1ca248998175180aa9c2eacd0_368   \n", "\n", "                                              function  target  \\\n", "0    static int decode_cblk(Jpeg2000DecoderContext ...       1   \n", "1    int yr_object_array_set_item(\\n    YR_OBJECT* ...       1   \n", "2    static int crypto_report_cipher(struct sk_buff...       1   \n", "3    tmsize_t\\nTIFFReadEncodedStrip(TIFF* tif, uint...       1   \n", "4    void ArrowHead()\\n/*\\n    Jaxodraw style arrow...       1   \n", "..                                                 ...     ...   \n", "330  int mp4client_main(int argc, char **argv)\\n{\\n...       1   \n", "331  DltReturnValue dlt_file_message(DltFile *file,...       1   \n", "332  static int readContigStripsIntoBuffer (TIFF* i...       1   \n", "333  MagickPPExport void Magick::throwException(Exc...       1   \n", "334  BOOL update_recv(rdpUpdate* update, wStream* s...       1   \n", "\n", "                                   function_numbers  \\\n", "0    {'function_start': 1019, 'function_end': 1071}   \n", "1      {'function_start': 711, 'function_end': 760}   \n", "2        {'function_start': 74, 'function_end': 91}   \n", "3      {'function_start': 321, 'function_end': 384}   \n", "4      {'function_start': 485, 'function_end': 522}   \n", "..                                              ...   \n", "330  {'function_start': 1193, 'function_end': 2382}   \n", "331  {'function_start': 1713, 'function_end': 1747}   \n", "332  {'function_start': 3673, 'function_end': 3705}   \n", "333    {'function_start': 819, 'function_end': 977}   \n", "334    {'function_start': 758, 'function_end': 835}   \n", "\n", "                                          line_numbers  \\\n", "0    [{'line_change': '@@  -1025,6 +1030,9  @@ stat...   \n", "1    [{'line_change': '@@  -725,7 +725,10  @@ int y...   \n", "2    [{'line_change': '@@  -75,7 +75,7  @@ static i...   \n", "3    [{'line_change': '@@  -346,7 +346,7  @@ TIFFRe...   \n", "4    [{'line_change': '@@  -503,10 +522,10  @@ void...   \n", "..                                                 ...   \n", "330  [{'line_change': '@@  -1680,7 +1694,14  @@ int...   \n", "331  [{'line_change': '@@  -1718,7 +1718,7  @@ DltR...   \n", "332  [{'line_change': '@@  -3698,7 +3698,7  @@ stat...   \n", "333  [{'line_change': '@@  -852,12 +852,18  @@ Magi...   \n", "334  [{'line_change': '@@  -807,7 +810,8  @@ BOOL u...   \n", "\n", "                                    commit_id          cve_id cve_language  \\\n", "0    9a271a9368eaabf99e6c2046103acb33957e63b7   CVE-2013-7018            C   \n", "1    4a342f01e5439b9bb901aff1c6c23c536baeeb3f  CVE-2017-11328            C   \n", "2    9a5467bf7b6e9e02ec9c3da4e23747c05faeaac6   CVE-2013-2546            C   \n", "3    438274f938e046d33cb0e1230b41da32ffe223e1  CVE-2016-10266            C   \n", "4    9216833a3888a4105a18e8c349f65b045ddb1079  CVE-2019-18604            C   \n", "..                                        ...             ...          ...   \n", "330  35ab4475a7df9b2a4bcab235e379c0c3ec543658  CVE-2018-20762            C   \n", "331  8ac9a080bee25e67e49bd138d81c992ce7b6d899  CVE-2023-36321            C   \n", "332  9657bbe3cdce4aaa90e07d50c1c70ae52da0ba6a  CVE-2016-10092            C   \n", "333  3358f060fc182551822576b2c0a8850faab5d543   CVE-2017-6499          C++   \n", "334  c367f65d42e0d2e1ca248998175180aa9c2eacd0  CVE-2020-11049            C   \n", "\n", "                                       cve_description  cvss  ...  \\\n", "0    libavcodec/jpeg2000dec.c in FFmpeg before 2.1 ...   5.6  ...   \n", "1    Heap buffer overflow in the yr_object_array_se...   5.5  ...   \n", "2    The report API in the crypto user configuratio...   9.8  ...   \n", "3    LibTIFF 4.0.7 allows remote attackers to cause...   5.5  ...   \n", "4    In axohelp.c before 1.3 in axohelp in axodraw2...   9.8  ...   \n", "..                                                 ...   ...  ...   \n", "330  GPAC version 0.7.1 and earlier has a buffer ov...   7.8  ...   \n", "331  Connected Vehicle Systems Alliance (COVESA) up...   7.5  ...   \n", "332  Heap-based buffer overflow in the readContigSt...   7.8  ...   \n", "333  An issue was discovered in Magick++ in ImageMa...   5.5  ...   \n", "334  In FreeRDP after 1.1 and before 2.0.0, there i...   2.2  ...   \n", "\n", "                                                   url          file_path  \\\n", "0    https://github.com/FFmpeg/FFmpeg/raw/9a271a936...   files/2013_12/31   \n", "1    https://github.com/VirusTotal/yara/raw/4a342f0...    files/2017_7/42   \n", "2    https://github.com/torvalds/linux/raw/9a5467bf...    files/2013_3/34   \n", "3    https://github.com/vadz/libtiff/raw/438274f938...   files/2017_3/245   \n", "4    https://github.com/TeX-Live/texlive-source/raw...   files/2019_10\\43   \n", "..                                                 ...                ...   \n", "330  https://github.com/gpac/gpac/raw/35ab4475a7df9...    files/2019_2\\97   \n", "331  https://github.com/micha<PERSON>-methn<PERSON>/dlt-daemon/...  files/2023_10/762   \n", "332  https://github.com/vadz/libtiff/raw/9657bbe3cd...  files/2017_3/1057   \n", "333  https://github.com/ImageMagick/ImageMagick/raw...   files/2017_3/898   \n", "334  https://github.com/FreeRDP/FreeRDP/raw/c367f65...   files/2020_5/200   \n", "\n", "                                 file_name file_language file_target  \\\n", "0                 libavcodec/jpeg2000dec.c             c           1   \n", "1                         libyara/object.c             c          -1   \n", "2                     crypto/crypto_user.c             c           1   \n", "3                       libtiff/tif_read.c             c          -1   \n", "4    utils/axodraw2/axodraw2-src/axohelp.c             c          -1   \n", "..                                     ...           ...         ...   \n", "330          applications/mp4client/main.c             c           1   \n", "331                src/shared/dlt_common.c             c          -1   \n", "332                       tools/tiffcrop.c             c          -1   \n", "333             Magick++/lib/Exception.cpp           cpp          -1   \n", "334               libfreerdp/core/update.c             c          -1   \n", "\n", "                                                static  \\\n", "0    {'flawfinder': [False, []], 'rats': [False, []...   \n", "1    {'flawfinder': [False, []], 'rats': [False, []...   \n", "2    {'flawfinder': [True, ['/data/rdhu/other/Stati...   \n", "3    {'flawfinder': [<PERSON>alse, []], 'rats': [<PERSON>als<PERSON>, []...   \n", "4    {'flawfinder': [True, ['/data/rdhu/other/Stati...   \n", "..                                                 ...   \n", "330  {'flawfinder': [True, ['/data/rdhu/other/Stati...   \n", "331  {'flawfinder': [<PERSON>alse, []], 'rats': [<PERSON>als<PERSON>, []...   \n", "332  {'flawfinder': [<PERSON>alse, []], 'rats': [<PERSON>als<PERSON>, []...   \n", "333  {'flawfinder': [<PERSON>alse, []], 'rats': [<PERSON>als<PERSON>, []...   \n", "334  {'flawfinder': [<PERSON>alse, []], 'rats': [<PERSON>als<PERSON>, []...   \n", "\n", "                                                caller  \\\n", "0                                                   {}   \n", "1                                                   {}   \n", "2                                                   {}   \n", "3    {'libtiff.tif_read.TIFFCheckRead': 'static int...   \n", "4                                                   {}   \n", "..                                                 ...   \n", "330                                                 {}   \n", "331  {'src.shared.dlt_common.dlt_vlog': 'DltReturnV...   \n", "332                                                 {}   \n", "333                                                 {}   \n", "334  {'libfreerdp.core.update.update_begin_paint': ...   \n", "\n", "                                                callee  \\\n", "0    {'libavcodec.jpeg2000dec.jpeg2000_decode_tile'...   \n", "1                                                   {}   \n", "2                                                   {}   \n", "3    {'libtiff.tif_getimage.gtStripContig': 'static...   \n", "4                                                   {}   \n", "..                                                 ...   \n", "330                                                 {}   \n", "331  {'src.core_dump_handler.dlt_cdh.main': 'int ma...   \n", "332  {'tools.tiffcrop.loadImage': 'static int\n", "loadI...   \n", "333                                                 {}   \n", "334  {'libfreerdp.core.rdp.rdp_recv_data_pdu': 'int...   \n", "\n", "                                      caller_of_change  \\\n", "0                                                   {}   \n", "1                                                   {}   \n", "2                                                   {}   \n", "3                                                   {}   \n", "4                                                   {}   \n", "..                                                 ...   \n", "330                                                 {}   \n", "331  {'src.shared.dlt_common.dlt_vlog': 'DltReturnV...   \n", "332                                                 {}   \n", "333                                                 {}   \n", "334  {'libfreerdp.core.update.update_read_synchroni...   \n", "\n", "                                      callee_of_change  \n", "0                                                   {}  \n", "1                                                   {}  \n", "2                                                   {}  \n", "3                                                   {}  \n", "4                                                   {}  \n", "..                                                 ...  \n", "330                                                 {}  \n", "331                                                 {}  \n", "332                                                 {}  \n", "333                                                 {}  \n", "334  {'libfreerdp.core.update.update_recv': 'BOOL u...  \n", "\n", "[335 rows x 39 columns]"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "import re\n", "\n", "def line_changes_within_function(row):\n", "    \"\"\"\n", "    Returns True if all line changes fall within the function range.\n", "    \"\"\"\n", "    fn_start = row['function_numbers']['function_start']\n", "    fn_end = row['function_numbers']['function_end']\n", "    for change in row['line_numbers']:\n", "        start = change['line_start']\n", "        end = change['line_end']\n", "        if start is None or end is None:\n", "            return False\n", "        if start < fn_start or end > fn_end:\n", "            return False\n", "    return True\n", "\n", "# Filter the DataFrame\n", "cleaned_df = filtered_df[filtered_df.apply(line_changes_within_function, axis=1)].copy().reset_index(drop=True)\n", "cleaned_df"]}, {"cell_type": "code", "execution_count": 8, "id": "37a7cffe", "metadata": {}, "outputs": [{"data": {"text/plain": ["function_id               9a271a9368eaabf99e6c2046103acb33957e63b7_21\n", "function            static int decode_cblk(Jpeg2000DecoderContext ...\n", "target                                                              1\n", "function_numbers       {'function_start': 1019, 'function_end': 1071}\n", "line_numbers        [{'line_change': '@@  -1025,6 +1030,9  @@ stat...\n", "commit_id                    9a271a9368eaabf99e6c2046103acb33957e63b7\n", "cve_id                                                  CVE-2013-7018\n", "cve_language                                                        C\n", "cve_description     libavcodec/jpeg2000dec.c in FFmpeg before 2.1 ...\n", "cvss                                                              5.6\n", "publish_date                                         December 9, 2013\n", "cwe_id                                                      [CWE-119]\n", "cwe_description     The product performs operations on a memory bu...\n", "cwe_consequence     ::SCOPE:Integrity:SCOPE:Confidentiality:SCOPE:...\n", "cwe_method          ::METHOD:Automated Static Analysis:DESCRIPTION...\n", "cwe_solution        ::PHASE:Requirements:STRATEGY:Language Selecti...\n", "AV                                                            NETWORK\n", "AC                                                            NETWORK\n", "PR                                                               NONE\n", "UI                                                               NONE\n", "S                                                           UNCHANGED\n", "C                                                                 LOW\n", "I                                                                 LOW\n", "A                                                                 LOW\n", "commit_message      jpeg2000: check log2_cblk dimensions\\n\\nFixes ...\n", "commit_date                                      2013-08-24T01:42:54Z\n", "parents             [{'commit_id_before': 'b8ff4f5ea3d321b7922e686...\n", "project                                                 ffmpeg/ffmpeg\n", "outdated                                                            0\n", "url                 https://github.com/FFmpeg/FFmpeg/raw/9a271a936...\n", "file_path                                            files/2013_12/31\n", "file_name                                    libavcodec/jpeg2000dec.c\n", "file_language                                                       c\n", "file_target                                                         1\n", "static              {'flawfinder': [False, []], 'rats': [False, []...\n", "caller                                                             {}\n", "callee              {'libavcodec.jpeg2000dec.jpeg2000_decode_tile'...\n", "caller_of_change                                                   {}\n", "callee_of_change                                                   {}\n", "Name: 0, dtype: object"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["cleaned_df.loc[0]"]}, {"cell_type": "code", "execution_count": 9, "id": "0bbd110e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Func:\n", "{'function_start': 7546, 'function_end': 7591}\n"]}], "source": ["print(\"Func:\")\n", "i = 100\n", "print(cleaned_df.iloc[i][\"function_numbers\"])\n"]}, {"cell_type": "code", "execution_count": 10, "id": "6d87ce40", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'line_change': '@@  -7554,23 +7578,23  @@ createImageSection(uint32_t sectsize, unsigned char **sect_buff_ptr)\\n \\n   if (!sect_buff)\\n     {\\n-    sect_buff = (unsigned char *)limitMalloc(sectsize);\\n+    sect_buff = (unsigned char *)limitMalloc(sectsize + NUM_BUFF_OVERSIZE_BYTES);\\n     if (!sect_buff)\\n     {\\n         TIFFError(\"createImageSection\", \"Unable to allocate/reallocate section buffer\");\\n         return (-1);\\n     }\\n-    _TIFFmemset(sect_buff, 0, sectsize);\\n+    _TIFFmemset(sect_buff, 0, sectsize + NUM_BUFF_OVERSIZE_BYTES);\\n     }\\n   else\\n     {\\n     if (prev_sectsize < sectsize)\\n       {\\n-      new_buff = _TIFFrealloc(sect_buff, sectsize);\\n+      new_buff = _TIFFrealloc(sect_buff, sectsize + NUM_BUFF_OVERSIZE_BYTES);\\n       if (!new_buff)\\n         {\\n           _TIFFfree (sect_buff);\\n-        sect_buff = (unsigned char *)limitMalloc(sectsize);\\n+        sect_buff = (unsigned char *)limitMalloc(sectsize + NUM_BUFF_OVERSIZE_BYTES);\\n         }\\n       else\\n         sect_buff = new_buff;\\n',\n", "  'line_start': 7554,\n", "  'line_end': 7576},\n", " {'line_change': '@@  -7580,7 +7604,7  @@ createImageSection(uint32_t sectsize, unsigned char **sect_buff_ptr)\\n           TIFFError(\"createImageSection\", \"Unable to allocate/reallocate section buffer\");\\n           return (-1);\\n       }\\n-      _TIFFmemset(sect_buff, 0, sectsize);\\n+      _TIFFmemset(sect_buff, 0, sectsize + NUM_BUFF_OVERSIZE_BYTES);\\n       }\\n     }\\n \\n',\n", "  'line_start': 7580,\n", "  'line_end': 7586}]"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["cleaned_df.iloc[i][\"line_numbers\"]"]}, {"cell_type": "code", "execution_count": 11, "id": "95e6e1b7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Number of commits with more than one row in cleaned_df: 16\n"]}], "source": ["commit_counts = cleaned_df['commit_id'].value_counts()\n", "num_commits_with_multiple_rows = (commit_counts > 1).sum()\n", "print(\"Number of commits with more than one row in cleaned_df:\", num_commits_with_multiple_rows)"]}, {"cell_type": "code", "execution_count": null, "id": "4ee2e39f", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>function_id</th>\n", "      <th>function</th>\n", "      <th>target</th>\n", "      <th>function_numbers</th>\n", "      <th>line_numbers</th>\n", "      <th>commit_id</th>\n", "      <th>cve_id</th>\n", "      <th>cve_language</th>\n", "      <th>cve_description</th>\n", "      <th>cvss</th>\n", "      <th>...</th>\n", "      <th>url</th>\n", "      <th>file_path</th>\n", "      <th>file_name</th>\n", "      <th>file_language</th>\n", "      <th>file_target</th>\n", "      <th>static</th>\n", "      <th>caller</th>\n", "      <th>callee</th>\n", "      <th>caller_of_change</th>\n", "      <th>callee_of_change</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>9a5467bf7b6e9e02ec9c3da4e23747c05faeaac6_118</td>\n", "      <td>static int crypto_report_cipher(struct sk_buff...</td>\n", "      <td>1</td>\n", "      <td>{'function_start': 74, 'function_end': 91}</td>\n", "      <td>[{'line_change': '@@  -75,7 +75,7  @@ static i...</td>\n", "      <td>9a5467bf7b6e9e02ec9c3da4e23747c05faeaac6</td>\n", "      <td>CVE-2013-2546</td>\n", "      <td>C</td>\n", "      <td>The report API in the crypto user configuratio...</td>\n", "      <td>9.8</td>\n", "      <td>...</td>\n", "      <td>https://github.com/torvalds/linux/raw/9a5467bf...</td>\n", "      <td>files/2013_3/34</td>\n", "      <td>crypto/crypto_user.c</td>\n", "      <td>c</td>\n", "      <td>1</td>\n", "      <td>{'flawfinder': [True, ['/data/rdhu/other/Stati...</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>d919b2744cd05abae043490f0a3dd1946c1ccb8c_20</td>\n", "      <td>static int on_part_data(\\n        multipart_pa...</td>\n", "      <td>1</td>\n", "      <td>{'function_start': 1861, 'function_end': 1910}</td>\n", "      <td>[{'line_change': '@@  -1867,7 +1865,7  @@ stat...</td>\n", "      <td>d919b2744cd05abae043490f0a3dd1946c1ccb8c</td>\n", "      <td>CVE-2021-44109</td>\n", "      <td>C</td>\n", "      <td>A buffer overflow in lib/sbi/message.c in Open...</td>\n", "      <td>7.5</td>\n", "      <td>...</td>\n", "      <td>https://github.com/open5gs/open5gs/raw/d919b27...</td>\n", "      <td>files/2022_4/715</td>\n", "      <td>lib/sbi/message.c</td>\n", "      <td>c</td>\n", "      <td>-1</td>\n", "      <td>{'flawfinder': [False, []], 'rats': [False, []...</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>fd6330292501983ac75fe4162275794b18445bd9_117</td>\n", "      <td>float CLASS find_green (int bps, int bite, int...</td>\n", "      <td>1</td>\n", "      <td>{'function_start': 13737, 'function_end': 13760}</td>\n", "      <td>[{'line_change': '@@  -13739,7 +13794,8  @@ fl...</td>\n", "      <td>fd6330292501983ac75fe4162275794b18445bd9</td>\n", "      <td>CVE-2018-5810</td>\n", "      <td>C++</td>\n", "      <td>An error within the \"rollei_load_raw()\" functi...</td>\n", "      <td>8.8</td>\n", "      <td>...</td>\n", "      <td>https://github.com/LibRaw/LibRaw/raw/fd6330292...</td>\n", "      <td>files/2018_12\\216</td>\n", "      <td>internal/dcraw_common.cpp</td>\n", "      <td>cpp</td>\n", "      <td>-1</td>\n", "      <td>{'flawfinder': [True, ['/data/rdhu/other/Stati...</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2b3dedfb9ba13f15887f22b935d373f36c9a59fa_57</td>\n", "      <td>void\\npci_lintr_release(struct pci_vdev *dev)\\...</td>\n", "      <td>1</td>\n", "      <td>{'function_start': 1776, 'function_end': 1793}</td>\n", "      <td>[{'line_change': '@@  -1781,7 +1800,10  @@ pci...</td>\n", "      <td>2b3dedfb9ba13f15887f22b935d373f36c9a59fa</td>\n", "      <td>CVE-2019-18844</td>\n", "      <td>C</td>\n", "      <td>The Device Model in ACRN before 2019w25.5-1400...</td>\n", "      <td>7.5</td>\n", "      <td>...</td>\n", "      <td>https://github.com/projectacrn/acrn-hypervisor...</td>\n", "      <td>files/2019_11\\369</td>\n", "      <td>devicemodel/hw/pci/core.c</td>\n", "      <td>c</td>\n", "      <td>-1</td>\n", "      <td>{'flawfinder': [False, []], 'rats': [False, []...</td>\n", "      <td>{}</td>\n", "      <td>{'devicemodel.hw.pci.wdt_i6300esb.pci_wdt_dein...</td>\n", "      <td>{}</td>\n", "      <td>{'devicemodel.hw.pci.core.pci_emul_deinit': 's...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>00383e162ae2f8fc951f5745bf1011771acb8dce_16</td>\n", "      <td>opj_pi_iterator_t *opj_pi_initialise_encode(co...</td>\n", "      <td>1</td>\n", "      <td>{'function_start': 1580, 'function_end': 1770}</td>\n", "      <td>[{'line_change': '@@  -1580,7 +1594,8  @@ OPJ_...</td>\n", "      <td>00383e162ae2f8fc951f5745bf1011771acb8dce</td>\n", "      <td>CVE-2020-27841</td>\n", "      <td>C</td>\n", "      <td>There's a flaw in openjpeg in versions prior t...</td>\n", "      <td>5.5</td>\n", "      <td>...</td>\n", "      <td>https://github.com/uclouvain/openjpeg/raw/0038...</td>\n", "      <td>files/2021_1/414</td>\n", "      <td>src/lib/openjp2/pi.c</td>\n", "      <td>c</td>\n", "      <td>-1</td>\n", "      <td>{'flawfinder': [False, []], 'rats': [False, []...</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>c6247a9ef2c7b33244ab1d3aa5d629ec49f0a067_41</td>\n", "      <td>static void\\nusage(const char *prog)\\n{\\n\\tfpr...</td>\n", "      <td>1</td>\n", "      <td>{'function_start': 1074, 'function_end': 1172}</td>\n", "      <td>[{'line_change': '@@  -1090,6 +1117,7  @@ usag...</td>\n", "      <td>c6247a9ef2c7b33244ab1d3aa5d629ec49f0a067</td>\n", "      <td>CVE-2018-19045</td>\n", "      <td>C</td>\n", "      <td>keepalived 2.0.8 used mode 0666 when creating ...</td>\n", "      <td>7.5</td>\n", "      <td>...</td>\n", "      <td>https://github.com/acassen/keepalived/raw/c624...</td>\n", "      <td>files/2018_11\\36</td>\n", "      <td>keepalived/core/main.c</td>\n", "      <td>c</td>\n", "      <td>1</td>\n", "      <td>{'flawfinder': [True, ['/data/rdhu/other/Stati...</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>ebad9ddc4efb2635f37174c9d800d06206f1edf9_63</td>\n", "      <td>int options_parse(CONF_TYPE type) {\\n    SERVI...</td>\n", "      <td>1</td>\n", "      <td>{'function_start': 377, 'function_end': 389}</td>\n", "      <td>[{'line_change': '@@  -379,11 +379,14  @@ int ...</td>\n", "      <td>ebad9ddc4efb2635f37174c9d800d06206f1edf9</td>\n", "      <td>CVE-2021-20230</td>\n", "      <td>C</td>\n", "      <td>A flaw was found in stunnel before 5.57, where...</td>\n", "      <td>7.5</td>\n", "      <td>...</td>\n", "      <td>https://github.com/mtrojnar/stunnel/raw/ebad9d...</td>\n", "      <td>files/2021_2/109</td>\n", "      <td>src/options.c</td>\n", "      <td>c</td>\n", "      <td>-1</td>\n", "      <td>{'flawfinder': [True, ['/data/rdhu/other/Stati...</td>\n", "      <td>{'src.options.options_defaults': 'void options...</td>\n", "      <td>{'src.options.options_cmdline': 'int options_c...</td>\n", "      <td>{'src.options.options_defaults': 'void options...</td>\n", "      <td>{'src.options.options_cmdline': 'int options_c...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>048a91e2682c1a8936ae34dbc7bd70291ec05410_157</td>\n", "      <td>bool SFD_GetFontMetaData( FILE *sfd,\\n\\t\\t\\t  ...</td>\n", "      <td>1</td>\n", "      <td>{'function_start': 7628, 'function_end': 8453}</td>\n", "      <td>[{'line_change': '@@  -7992,10 +7995,12  @@ bo...</td>\n", "      <td>048a91e2682c1a8936ae34dbc7bd70291ec05410</td>\n", "      <td>CVE-2020-25690</td>\n", "      <td>C</td>\n", "      <td>An out-of-bounds write flaw was found in FontF...</td>\n", "      <td>8.8</td>\n", "      <td>...</td>\n", "      <td>https://github.com/fontforge/fontforge/raw/048...</td>\n", "      <td>files/2021_2/162</td>\n", "      <td>fontforge/sfd.c</td>\n", "      <td>c</td>\n", "      <td>1</td>\n", "      <td>{'flawfinder': [False, []], 'rats': [True, ['/...</td>\n", "      <td>{'fontforge.sfd.SFD_GetFontMetaDataData_Init':...</td>\n", "      <td>{'fontforge.sfd.SFD_GetFontMetaDataVoid': 'voi...</td>\n", "      <td>{'Unicode.char.strmatch': 'int strmatch(const ...</td>\n", "      <td>{'fontforge.sfd.SFD_GetFontMetaData': 'bool SF...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>97b153237c256c586e528eac7fc2f51aedb2b2fc_14</td>\n", "      <td>static int\\npci_emul_alloc_resource(uint64_t *...</td>\n", "      <td>1</td>\n", "      <td>{'function_start': 470, 'function_end': 486}</td>\n", "      <td>[{'line_change': '@@  -473,7 +472,10  @@ pci_e...</td>\n", "      <td>97b153237c256c586e528eac7fc2f51aedb2b2fc</td>\n", "      <td>CVE-2019-18844</td>\n", "      <td>C</td>\n", "      <td>The Device Model in ACRN before 2019w25.5-1400...</td>\n", "      <td>7.5</td>\n", "      <td>...</td>\n", "      <td>https://github.com/shuox/acrn-hypervisor/raw/9...</td>\n", "      <td>files/2019_11\\372</td>\n", "      <td>devicemodel/hw/pci/core.c</td>\n", "      <td>c</td>\n", "      <td>-1</td>\n", "      <td>{'flawfinder': [False, []], 'rats': [False, []...</td>\n", "      <td>{}</td>\n", "      <td>{'devicemodel.hw.pci.core.pci_emul_alloc_pbar'...</td>\n", "      <td>{}</td>\n", "      <td>{'devicemodel.hw.pci.core.pci_emul_alloc_pbar'...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>2e6173c273ca14adb11386db4e47622552b1c00e_74</td>\n", "      <td>TPM_RC\\nTPMT_TK_AUTH_Unmarshal(TPMT_TK_AUTH *t...</td>\n", "      <td>1</td>\n", "      <td>{'function_start': 1749, 'function_end': 1770}</td>\n", "      <td>[{'line_change': '@@  -1750,6 +1846,7  @@ TPM_...</td>\n", "      <td>2e6173c273ca14adb11386db4e47622552b1c00e</td>\n", "      <td>CVE-2021-3623</td>\n", "      <td>C</td>\n", "      <td>A flaw was found in libtpms. The flaw can be t...</td>\n", "      <td>6.2</td>\n", "      <td>...</td>\n", "      <td>https://github.com/stefanberger/libtpms/raw/2e...</td>\n", "      <td>files/2022_3/1004</td>\n", "      <td>src/tpm2/Unmarshal.c</td>\n", "      <td>c</td>\n", "      <td>-1</td>\n", "      <td>{'flawfinder': [False, []], 'rats': [False, []...</td>\n", "      <td>{'src.tpm2.Unmarshal.TPM_ST_Unmarshal': 'TPM_R...</td>\n", "      <td>{}</td>\n", "      <td>{'src.tpm2.Unmarshal.TPM_ST_Unmarshal': 'TPM_R...</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>d919b2744cd05abae043490f0a3dd1946c1ccb8c_21</td>\n", "      <td>static int on_part_data_end(multipart_parser *...</td>\n", "      <td>1</td>\n", "      <td>{'function_start': 1912, 'function_end': 1923}</td>\n", "      <td>[{'line_change': '@@  -1917,7 +1915,9  @@ stat...</td>\n", "      <td>d919b2744cd05abae043490f0a3dd1946c1ccb8c</td>\n", "      <td>CVE-2021-44108</td>\n", "      <td>C</td>\n", "      <td>A null pointer dereference in src/amf/namf-han...</td>\n", "      <td>7.5</td>\n", "      <td>...</td>\n", "      <td>https://github.com/open5gs/open5gs/raw/d919b27...</td>\n", "      <td>files/2022_4/717</td>\n", "      <td>lib/sbi/message.c</td>\n", "      <td>c</td>\n", "      <td>-1</td>\n", "      <td>{'flawfinder': [False, []], 'rats': [False, []...</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>b51b33f2bc5d1497ddf5bd107f791c101695000d_16</td>\n", "      <td>OM_uint32 KRB5_CALLCONV\\nspnego_gss_init_sec_c...</td>\n", "      <td>1</td>\n", "      <td>{'function_start': 981, 'function_end': 1143}</td>\n", "      <td>[{'line_change': '@@  -1108,16 +1107,11  @@ sp...</td>\n", "      <td>b51b33f2bc5d1497ddf5bd107f791c101695000d</td>\n", "      <td>CVE-2015-2695</td>\n", "      <td>C</td>\n", "      <td>lib/gssapi/spnego/spnego_mech.c in MIT Kerbero...</td>\n", "      <td>5.3</td>\n", "      <td>...</td>\n", "      <td>https://github.com/krb5/krb5/raw/b51b33f2bc5d1...</td>\n", "      <td>files/2015_11/16</td>\n", "      <td>src/lib/gssapi/spnego/spnego_mech.c</td>\n", "      <td>c</td>\n", "      <td>-1</td>\n", "      <td>{'flawfinder': [False, []], 'rats': [False, []...</td>\n", "      <td>{'src.appl.gss-sample.gss-misc.send_token': 'i...</td>\n", "      <td>{}</td>\n", "      <td>{'src.lib.gssapi.mechglue.g_rel_buffer.gss_rel...</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>00383e162ae2f8fc951f5745bf1011771acb8dce_4</td>\n", "      <td>static OPJ_BOOL opj_pi_next_pcrl(opj_pi_iterat...</td>\n", "      <td>1</td>\n", "      <td>{'function_start': 472, 'function_end': 601}</td>\n", "      <td>[{'line_change': '@@  -475,6 +471,13  @@ stati...</td>\n", "      <td>00383e162ae2f8fc951f5745bf1011771acb8dce</td>\n", "      <td>CVE-2020-27841</td>\n", "      <td>C</td>\n", "      <td>There's a flaw in openjpeg in versions prior t...</td>\n", "      <td>5.5</td>\n", "      <td>...</td>\n", "      <td>https://github.com/uclouvain/openjpeg/raw/0038...</td>\n", "      <td>files/2021_1/414</td>\n", "      <td>src/lib/openjp2/pi.c</td>\n", "      <td>c</td>\n", "      <td>-1</td>\n", "      <td>{'flawfinder': [False, []], 'rats': [False, []...</td>\n", "      <td>{}</td>\n", "      <td>{'src.lib.openjp2.pi.opj_pi_next': 'OPJ_BOOL o...</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>afd7086090dafd3949afd172822cbcec4ed17d56_6</td>\n", "      <td>static int writeBufferToSeparateTiles (TIFF* o...</td>\n", "      <td>1</td>\n", "      <td>{'function_start': 1357, 'function_end': 1431}</td>\n", "      <td>[{'line_change': '@@  -1358,7 +1370,8  @@ stat...</td>\n", "      <td>afd7086090dafd3949afd172822cbcec4ed17d56</td>\n", "      <td>CVE-2023-30775</td>\n", "      <td>C</td>\n", "      <td>A vulnerability was found in the libtiff libra...</td>\n", "      <td>5.5</td>\n", "      <td>...</td>\n", "      <td>https://github.com/libsdl-org/libtiff/raw/afd7...</td>\n", "      <td>files/2023_5/521</td>\n", "      <td>tools/tiffcrop.c</td>\n", "      <td>c</td>\n", "      <td>1</td>\n", "      <td>{'flawfinder': [True, ['/data/rdhu/other/Stati...</td>\n", "      <td>{}</td>\n", "      <td>{'tools.tiffcrop.writeSingleSection': 'static ...</td>\n", "      <td>{}</td>\n", "      <td>{'tools.tiffcrop.writeSingleSection': 'static ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>afd7086090dafd3949afd172822cbcec4ed17d56_60</td>\n", "      <td>static int\\ncreateImageSection(uint32_t sectsi...</td>\n", "      <td>1</td>\n", "      <td>{'function_start': 7546, 'function_end': 7591}</td>\n", "      <td>[{'line_change': '@@  -7554,23 +7578,23  @@ cr...</td>\n", "      <td>afd7086090dafd3949afd172822cbcec4ed17d56</td>\n", "      <td>CVE-2023-30775</td>\n", "      <td>C</td>\n", "      <td>A vulnerability was found in the libtiff libra...</td>\n", "      <td>5.5</td>\n", "      <td>...</td>\n", "      <td>https://github.com/libsdl-org/libtiff/raw/afd7...</td>\n", "      <td>files/2023_5/521</td>\n", "      <td>tools/tiffcrop.c</td>\n", "      <td>c</td>\n", "      <td>1</td>\n", "      <td>{'flawfinder': [True, ['/data/rdhu/other/Stati...</td>\n", "      <td>{}</td>\n", "      <td>{'tools.tiffcrop.writeImageSections': 'static ...</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>2b3dedfb9ba13f15887f22b935d373f36c9a59fa_24</td>\n", "      <td>int\\npci_emul_add_capability(struct pci_vdev *...</td>\n", "      <td>1</td>\n", "      <td>{'function_start': 762, 'function_end': 799}</td>\n", "      <td>[{'line_change': '@@  -765,8 +770,6  @@ pci_em...</td>\n", "      <td>2b3dedfb9ba13f15887f22b935d373f36c9a59fa</td>\n", "      <td>CVE-2019-18844</td>\n", "      <td>C</td>\n", "      <td>The Device Model in ACRN before 2019w25.5-1400...</td>\n", "      <td>7.5</td>\n", "      <td>...</td>\n", "      <td>https://github.com/projectacrn/acrn-hypervisor...</td>\n", "      <td>files/2019_11\\369</td>\n", "      <td>devicemodel/hw/pci/core.c</td>\n", "      <td>c</td>\n", "      <td>-1</td>\n", "      <td>{'flawfinder': [False, []], 'rats': [False, []...</td>\n", "      <td>{}</td>\n", "      <td>{'devicemodel.hw.pci.virtio.virtio.virtio_set_...</td>\n", "      <td>{}</td>\n", "      <td>{'devicemodel.hw.pci.core.pci_emul_add_msicap'...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>afd7086090dafd3949afd172822cbcec4ed17d56_1</td>\n", "      <td>static int readContigTilesIntoBuffer (TIFF* in...</td>\n", "      <td>1</td>\n", "      <td>{'function_start': 799, 'function_end': 1003}</td>\n", "      <td>[{'line_change': '@@  -810,8 +814,8  @@ static...</td>\n", "      <td>afd7086090dafd3949afd172822cbcec4ed17d56</td>\n", "      <td>CVE-2023-30775</td>\n", "      <td>C</td>\n", "      <td>A vulnerability was found in the libtiff libra...</td>\n", "      <td>5.5</td>\n", "      <td>...</td>\n", "      <td>https://github.com/libsdl-org/libtiff/raw/afd7...</td>\n", "      <td>files/2023_5/521</td>\n", "      <td>tools/tiffcrop.c</td>\n", "      <td>c</td>\n", "      <td>1</td>\n", "      <td>{'flawfinder': [True, ['/data/rdhu/other/Stati...</td>\n", "      <td>{}</td>\n", "      <td>{'tools.tiffcrop.loadImage': 'static int\n", "loadI...</td>\n", "      <td>{}</td>\n", "      <td>{'tools.tiffcrop.loadImage': 'static int\n", "loadI...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>fd6330292501983ac75fe4162275794b18445bd9_80</td>\n", "      <td>void CLASS samsung_load_raw()\\n{\\n  int row, c...</td>\n", "      <td>1</td>\n", "      <td>{'function_start': 3569, 'function_end': 3601}</td>\n", "      <td>[{'line_change': '@@  -3569,6 +3582,11  @@ voi...</td>\n", "      <td>fd6330292501983ac75fe4162275794b18445bd9</td>\n", "      <td>CVE-2018-5810</td>\n", "      <td>C++</td>\n", "      <td>An error within the \"rollei_load_raw()\" functi...</td>\n", "      <td>8.8</td>\n", "      <td>...</td>\n", "      <td>https://github.com/LibRaw/LibRaw/raw/fd6330292...</td>\n", "      <td>files/2018_12\\216</td>\n", "      <td>internal/dcraw_common.cpp</td>\n", "      <td>cpp</td>\n", "      <td>-1</td>\n", "      <td>{'flawfinder': [True, ['/data/rdhu/other/Stati...</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>97b153237c256c586e528eac7fc2f51aedb2b2fc_13</td>\n", "      <td>static int\\npci_emul_mem_handler(struct vmctx ...</td>\n", "      <td>1</td>\n", "      <td>{'function_start': 420, 'function_end': 467}</td>\n", "      <td>[{'line_change': '@@  -426,11 +426,10  @@ pci_...</td>\n", "      <td>97b153237c256c586e528eac7fc2f51aedb2b2fc</td>\n", "      <td>CVE-2019-18844</td>\n", "      <td>C</td>\n", "      <td>The Device Model in ACRN before 2019w25.5-1400...</td>\n", "      <td>7.5</td>\n", "      <td>...</td>\n", "      <td>https://github.com/shuox/acrn-hypervisor/raw/9...</td>\n", "      <td>files/2019_11\\372</td>\n", "      <td>devicemodel/hw/pci/core.c</td>\n", "      <td>c</td>\n", "      <td>-1</td>\n", "      <td>{'flawfinder': [False, []], 'rats': [False, []...</td>\n", "      <td>{}</td>\n", "      <td>{'devicemodel.hw.pci.core.modify_bar_registrat...</td>\n", "      <td>{}</td>\n", "      <td>{'devicemodel.hw.pci.core.modify_bar_registrat...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>7ef09e1fc9ba07653dd078edb2408631c7969162_13</td>\n", "      <td>static int\\nfind_sig8_target_as_global_offset(...</td>\n", "      <td>1</td>\n", "      <td>{'function_start': 597, 'function_end': 624}</td>\n", "      <td>[{'line_change': '@@  -605,7 +605,6  @@ find_s...</td>\n", "      <td>7ef09e1fc9ba07653dd078edb2408631c7969162</td>\n", "      <td>CVE-2022-34299</td>\n", "      <td>C</td>\n", "      <td>There is a heap-based buffer over-read in libd...</td>\n", "      <td>8.1</td>\n", "      <td>...</td>\n", "      <td>https://github.com/davea42/libdwarf-code/raw/7...</td>\n", "      <td>files/2022_6/338</td>\n", "      <td>src/lib/libdwarf/dwarf_form.c</td>\n", "      <td>c</td>\n", "      <td>1</td>\n", "      <td>{'flawfinder': [True, ['/data/rdhu/other/Stati...</td>\n", "      <td>{}</td>\n", "      <td>{'src.lib.libdwarf.dwarf_form.dwarf_global_for...</td>\n", "      <td>{}</td>\n", "      <td>{'src.lib.libdwarf.dwarf_form.dwarf_global_for...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>afd7086090dafd3949afd172822cbcec4ed17d56_5</td>\n", "      <td>static int writeBufferToContigTiles (TIFF* out...</td>\n", "      <td>1</td>\n", "      <td>{'function_start': 1262, 'function_end': 1352}</td>\n", "      <td>[{'line_change': '@@  -1267,7 +1277,7  @@ stat...</td>\n", "      <td>afd7086090dafd3949afd172822cbcec4ed17d56</td>\n", "      <td>CVE-2023-30775</td>\n", "      <td>C</td>\n", "      <td>A vulnerability was found in the libtiff libra...</td>\n", "      <td>5.5</td>\n", "      <td>...</td>\n", "      <td>https://github.com/libsdl-org/libtiff/raw/afd7...</td>\n", "      <td>files/2023_5/521</td>\n", "      <td>tools/tiffcrop.c</td>\n", "      <td>c</td>\n", "      <td>1</td>\n", "      <td>{'flawfinder': [True, ['/data/rdhu/other/Stati...</td>\n", "      <td>{}</td>\n", "      <td>{'tools.tiffcrop.writeSingleSection': 'static ...</td>\n", "      <td>{}</td>\n", "      <td>{'tools.tiffcrop.writeSingleSection': 'static ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>bcac8e7acb514429fee9e0b5d0c7a0308fd4d76b_121</td>\n", "      <td>static ssize_t _hostsock_readv(\\n    oe_fd_t* ...</td>\n", "      <td>1</td>\n", "      <td>{'function_start': 829, 'function_end': 866}</td>\n", "      <td>[{'line_change': '@@  -835,21 +1068,43  @@ sta...</td>\n", "      <td>bcac8e7acb514429fee9e0b5d0c7a0308fd4d76b</td>\n", "      <td>CVE-2020-15224</td>\n", "      <td>C</td>\n", "      <td>In Open Enclave before version 0.12.0, an info...</td>\n", "      <td>6.8</td>\n", "      <td>...</td>\n", "      <td>https://github.com/openenclave/openenclave/raw...</td>\n", "      <td>files/2020_10/178</td>\n", "      <td>syscall/devices/hostsock/hostsock.c</td>\n", "      <td>c</td>\n", "      <td>-1</td>\n", "      <td>{'flawfinder': [False, []], 'rats': [False, []...</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>9a5467bf7b6e9e02ec9c3da4e23747c05faeaac6_119</td>\n", "      <td>static int crypto_report_comp(struct sk_buff *...</td>\n", "      <td>1</td>\n", "      <td>{'function_start': 93, 'function_end': 106}</td>\n", "      <td>[{'line_change': '@@  -94,8 +94,7  @@ static i...</td>\n", "      <td>9a5467bf7b6e9e02ec9c3da4e23747c05faeaac6</td>\n", "      <td>CVE-2013-2546</td>\n", "      <td>C</td>\n", "      <td>The report API in the crypto user configuratio...</td>\n", "      <td>9.8</td>\n", "      <td>...</td>\n", "      <td>https://github.com/torvalds/linux/raw/9a5467bf...</td>\n", "      <td>files/2013_3/34</td>\n", "      <td>crypto/crypto_user.c</td>\n", "      <td>c</td>\n", "      <td>1</td>\n", "      <td>{'flawfinder': [True, ['/data/rdhu/other/Stati...</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>e7e5cc9f68e895f59b317d4d1dac37ee15418094_145</td>\n", "      <td>void DebugAPI::slowPathTraceGeneratorFrame(JST...</td>\n", "      <td>1</td>\n", "      <td>{'function_start': 3809, 'function_end': 3879}</td>\n", "      <td>[{'line_change': '@@  -3863,7 +3860,9  @@ void...</td>\n", "      <td>e7e5cc9f68e895f59b317d4d1dac37ee15418094</td>\n", "      <td>CVE-2023-29543</td>\n", "      <td>C++</td>\n", "      <td>An attacker could have caused memory corruptio...</td>\n", "      <td>8.8</td>\n", "      <td>...</td>\n", "      <td>https://github.com/mozilla/gecko-dev/raw/e7e5c...</td>\n", "      <td>files/2023_6/909</td>\n", "      <td>js/src/debugger/Debugger.cpp</td>\n", "      <td>cpp</td>\n", "      <td>-1</td>\n", "      <td>{'flawfinder': [False, []], 'rats': [False, []...</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>da5c0f119203ad9728920456a0f52a6d850c01cd_12</td>\n", "      <td>int nfc_deactivate_target(struct nfc_dev *dev,...</td>\n", "      <td>1</td>\n", "      <td>{'function_start': 442, 'function_end': 475}</td>\n", "      <td>[{'line_change': '@@  -448,7 +448,7  @@ int nf...</td>\n", "      <td>da5c0f119203ad9728920456a0f52a6d850c01cd</td>\n", "      <td>CVE-2022-1974</td>\n", "      <td>C</td>\n", "      <td>A use-after-free flaw was found in the Linux k...</td>\n", "      <td>4.1</td>\n", "      <td>...</td>\n", "      <td>https://github.com/torvalds/linux/raw/da5c0f11...</td>\n", "      <td>files/2022_8/9</td>\n", "      <td>net/nfc/core.c</td>\n", "      <td>c</td>\n", "      <td>-1</td>\n", "      <td>{'flawfinder': [False, []], 'rats': [False, []...</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>97b153237c256c586e528eac7fc2f51aedb2b2fc_66</td>\n", "      <td>static void\\npci_emul_cmdsts_write(struct pci_...</td>\n", "      <td>1</td>\n", "      <td>{'function_start': 1979, 'function_end': 2043}</td>\n", "      <td>[{'line_change': '@@  -2031,7 +2060,8  @@ pci_...</td>\n", "      <td>97b153237c256c586e528eac7fc2f51aedb2b2fc</td>\n", "      <td>CVE-2019-18844</td>\n", "      <td>C</td>\n", "      <td>The Device Model in ACRN before 2019w25.5-1400...</td>\n", "      <td>7.5</td>\n", "      <td>...</td>\n", "      <td>https://github.com/shuox/acrn-hypervisor/raw/9...</td>\n", "      <td>files/2019_11\\372</td>\n", "      <td>devicemodel/hw/pci/core.c</td>\n", "      <td>c</td>\n", "      <td>-1</td>\n", "      <td>{'flawfinder': [False, []], 'rats': [False, []...</td>\n", "      <td>{}</td>\n", "      <td>{'devicemodel.hw.pci.core.pci_cfgrw': 'static ...</td>\n", "      <td>{}</td>\n", "      <td>{'devicemodel.hw.pci.core.pci_cfgrw': 'static ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>7ef09e1fc9ba07653dd078edb2408631c7969162_15</td>\n", "      <td>int\\ndwarf_global_formref_b(Dwarf_Attribute at...</td>\n", "      <td>1</td>\n", "      <td>{'function_start': 675, 'function_end': 883}</td>\n", "      <td>[{'line_change': '@@  -837,6 +836,13  @@ dwarf...</td>\n", "      <td>7ef09e1fc9ba07653dd078edb2408631c7969162</td>\n", "      <td>CVE-2022-34299</td>\n", "      <td>C</td>\n", "      <td>There is a heap-based buffer over-read in libd...</td>\n", "      <td>8.1</td>\n", "      <td>...</td>\n", "      <td>https://github.com/davea42/libdwarf-code/raw/7...</td>\n", "      <td>files/2022_6/338</td>\n", "      <td>src/lib/libdwarf/dwarf_form.c</td>\n", "      <td>c</td>\n", "      <td>1</td>\n", "      <td>{'flawfinder': [True, ['/data/rdhu/other/Stati...</td>\n", "      <td>{'src.lib.libdwarf.dwarf_form.get_attr_dbg': '...</td>\n", "      <td>{'src.lib.libdwarf.dwarf_form.dwarf_global_for...</td>\n", "      <td>{'src.lib.libdwarf.dwarf_form.find_sig8_target...</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>ebad9ddc4efb2635f37174c9d800d06206f1edf9_66</td>\n", "      <td>void options_defaults() {\\n    SERVICE_OPTIONS...</td>\n", "      <td>1</td>\n", "      <td>{'function_start': 586, 'function_end': 597}</td>\n", "      <td>[{'line_change': '@@  -591,7 +589,7  @@ void o...</td>\n", "      <td>ebad9ddc4efb2635f37174c9d800d06206f1edf9</td>\n", "      <td>CVE-2021-20230</td>\n", "      <td>C</td>\n", "      <td>A flaw was found in stunnel before 5.57, where...</td>\n", "      <td>7.5</td>\n", "      <td>...</td>\n", "      <td>https://github.com/mtrojnar/stunnel/raw/ebad9d...</td>\n", "      <td>files/2021_2/109</td>\n", "      <td>src/options.c</td>\n", "      <td>c</td>\n", "      <td>-1</td>\n", "      <td>{'flawfinder': [True, ['/data/rdhu/other/Stati...</td>\n", "      <td>{}</td>\n", "      <td>{'src.options.options_parse': 'int options_par...</td>\n", "      <td>{}</td>\n", "      <td>{'src.options.options_parse': 'int options_par...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>c6247a9ef2c7b33244ab1d3aa5d629ec49f0a067_43</td>\n", "      <td>int\\nkeepalived_main(int argc, char **argv)\\n{...</td>\n", "      <td>1</td>\n", "      <td>{'function_start': 1552, 'function_end': 1895}</td>\n", "      <td>[{'line_change': '@@  -1582,6 +1617,9  @@ keep...</td>\n", "      <td>c6247a9ef2c7b33244ab1d3aa5d629ec49f0a067</td>\n", "      <td>CVE-2018-19045</td>\n", "      <td>C</td>\n", "      <td>keepalived 2.0.8 used mode 0666 when creating ...</td>\n", "      <td>7.5</td>\n", "      <td>...</td>\n", "      <td>https://github.com/acassen/keepalived/raw/c624...</td>\n", "      <td>files/2018_11\\36</td>\n", "      <td>keepalived/core/main.c</td>\n", "      <td>c</td>\n", "      <td>1</td>\n", "      <td>{'flawfinder': [True, ['/data/rdhu/other/Stati...</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>afd7086090dafd3949afd172822cbcec4ed17d56_32</td>\n", "      <td>static int readContigStripsIntoBuffer (TIFF* i...</td>\n", "      <td>1</td>\n", "      <td>{'function_start': 3825, 'function_end': 3856}</td>\n", "      <td>[{'line_change': '@@  -3825,10 +3841,10  @@ ex...</td>\n", "      <td>afd7086090dafd3949afd172822cbcec4ed17d56</td>\n", "      <td>CVE-2023-30775</td>\n", "      <td>C</td>\n", "      <td>A vulnerability was found in the libtiff libra...</td>\n", "      <td>5.5</td>\n", "      <td>...</td>\n", "      <td>https://github.com/libsdl-org/libtiff/raw/afd7...</td>\n", "      <td>files/2023_5/521</td>\n", "      <td>tools/tiffcrop.c</td>\n", "      <td>c</td>\n", "      <td>1</td>\n", "      <td>{'flawfinder': [True, ['/data/rdhu/other/Stati...</td>\n", "      <td>{}</td>\n", "      <td>{'tools.tiffcrop.loadImage': 'static int\n", "loadI...</td>\n", "      <td>{}</td>\n", "      <td>{'tools.tiffcrop.loadImage': 'static int\n", "loadI...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>b51b33f2bc5d1497ddf5bd107f791c101695000d_37</td>\n", "      <td>OM_uint32 KRB5_CALLCONV\\nspnego_gss_wrap_size_...</td>\n", "      <td>1</td>\n", "      <td>{'function_start': 2220, 'function_end': 2237}</td>\n", "      <td>[{'line_change': '@@  -2227,8 +2275,13  @@ spn...</td>\n", "      <td>b51b33f2bc5d1497ddf5bd107f791c101695000d</td>\n", "      <td>CVE-2015-2695</td>\n", "      <td>C</td>\n", "      <td>lib/gssapi/spnego/spnego_mech.c in MIT Kerbero...</td>\n", "      <td>5.3</td>\n", "      <td>...</td>\n", "      <td>https://github.com/krb5/krb5/raw/b51b33f2bc5d1...</td>\n", "      <td>files/2015_11/16</td>\n", "      <td>src/lib/gssapi/spnego/spnego_mech.c</td>\n", "      <td>c</td>\n", "      <td>-1</td>\n", "      <td>{'flawfinder': [False, []], 'rats': [False, []...</td>\n", "      <td>{'src.lib.gssapi.mechglue.g_seal.gss_wrap_size...</td>\n", "      <td>{}</td>\n", "      <td>{'src.lib.gssapi.mechglue.g_seal.gss_wrap_size...</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>bcac8e7acb514429fee9e0b5d0c7a0308fd4d76b_118</td>\n", "      <td>static int _hostsock_getsockname(\\n    oe_fd_t...</td>\n", "      <td>1</td>\n", "      <td>{'function_start': 787, 'function_end': 817}</td>\n", "      <td>[{'line_change': '@@  -792,25 +1011,39  @@ sta...</td>\n", "      <td>bcac8e7acb514429fee9e0b5d0c7a0308fd4d76b</td>\n", "      <td>CVE-2020-15224</td>\n", "      <td>C</td>\n", "      <td>In Open Enclave before version 0.12.0, an info...</td>\n", "      <td>6.8</td>\n", "      <td>...</td>\n", "      <td>https://github.com/openenclave/openenclave/raw...</td>\n", "      <td>files/2020_10/178</td>\n", "      <td>syscall/devices/hostsock/hostsock.c</td>\n", "      <td>c</td>\n", "      <td>-1</td>\n", "      <td>{'flawfinder': [False, []], 'rats': [False, []...</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>048a91e2682c1a8936ae34dbc7bd70291ec05410_159</td>\n", "      <td>static SplineFont *SFD_GetFont( FILE *sfd,Spli...</td>\n", "      <td>1</td>\n", "      <td>{'function_start': 8462, 'function_end': 9019}</td>\n", "      <td>[{'line_change': '@@  -8948,6 +8953,10  @@ exi...</td>\n", "      <td>048a91e2682c1a8936ae34dbc7bd70291ec05410</td>\n", "      <td>CVE-2020-25690</td>\n", "      <td>C</td>\n", "      <td>An out-of-bounds write flaw was found in FontF...</td>\n", "      <td>8.8</td>\n", "      <td>...</td>\n", "      <td>https://github.com/fontforge/fontforge/raw/048...</td>\n", "      <td>files/2021_2/162</td>\n", "      <td>fontforge/sfd.c</td>\n", "      <td>c</td>\n", "      <td>1</td>\n", "      <td>{'flawfinder': [False, []], 'rats': [True, ['/...</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>e7e5cc9f68e895f59b317d4d1dac37ee15418094_100</td>\n", "      <td>bool DebugAPI::onSingleStep(JSContext* cx) {\\n...</td>\n", "      <td>1</td>\n", "      <td>{'function_start': 2599, 'function_end': 2724}</td>\n", "      <td>[{'line_change': '@@  -2624,7 +2629,8  @@ bool...</td>\n", "      <td>e7e5cc9f68e895f59b317d4d1dac37ee15418094</td>\n", "      <td>CVE-2023-29543</td>\n", "      <td>C++</td>\n", "      <td>An attacker could have caused memory corruptio...</td>\n", "      <td>8.8</td>\n", "      <td>...</td>\n", "      <td>https://github.com/mozilla/gecko-dev/raw/e7e5c...</td>\n", "      <td>files/2023_6/909</td>\n", "      <td>js/src/debugger/Debugger.cpp</td>\n", "      <td>cpp</td>\n", "      <td>-1</td>\n", "      <td>{'flawfinder': [False, []], 'rats': [False, []...</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>da5c0f119203ad9728920456a0f52a6d850c01cd_3</td>\n", "      <td>int nfc_dev_down(struct nfc_dev *dev)\\n{\\n\\tin...</td>\n", "      <td>1</td>\n", "      <td>{'function_start': 137, 'function_end': 168}</td>\n", "      <td>[{'line_change': '@@  -142,7 +142,7  @@ int nf...</td>\n", "      <td>da5c0f119203ad9728920456a0f52a6d850c01cd</td>\n", "      <td>CVE-2022-1974</td>\n", "      <td>C</td>\n", "      <td>A use-after-free flaw was found in the Linux k...</td>\n", "      <td>4.1</td>\n", "      <td>...</td>\n", "      <td>https://github.com/torvalds/linux/raw/da5c0f11...</td>\n", "      <td>files/2022_8/9</td>\n", "      <td>net/nfc/core.c</td>\n", "      <td>c</td>\n", "      <td>-1</td>\n", "      <td>{'flawfinder': [False, []], 'rats': [False, []...</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35</th>\n", "      <td>fd6330292501983ac75fe4162275794b18445bd9_99</td>\n", "      <td>void CLASS parse_makernote (int base, int upta...</td>\n", "      <td>1</td>\n", "      <td>{'function_start': 8261, 'function_end': 10105}</td>\n", "      <td>[{'line_change': '@@  -9890,37 +9914,68  @@ vo...</td>\n", "      <td>fd6330292501983ac75fe4162275794b18445bd9</td>\n", "      <td>CVE-2018-5812</td>\n", "      <td>C++</td>\n", "      <td>An error within the \"nikon_coolscan_load_raw()...</td>\n", "      <td>6.5</td>\n", "      <td>...</td>\n", "      <td>https://github.com/LibRaw/LibRaw/raw/fd6330292...</td>\n", "      <td>files/2018_12\\222</td>\n", "      <td>internal/dcraw_common.cpp</td>\n", "      <td>cpp</td>\n", "      <td>-1</td>\n", "      <td>{'flawfinder': [True, ['/data/rdhu/other/Stati...</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "      <td>{}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>2e6173c273ca14adb11386db4e47622552b1c00e_123</td>\n", "      <td>TPM_RC\\nTPMI_RSA_KEY_BITS_Unmarshal(TPMI_RSA_K...</td>\n", "      <td>1</td>\n", "      <td>{'function_start': 3477, 'function_end': 3496}</td>\n", "      <td>[{'line_change': '@@  -3478,6 +3596,7  @@ TPM_...</td>\n", "      <td>2e6173c273ca14adb11386db4e47622552b1c00e</td>\n", "      <td>CVE-2021-3623</td>\n", "      <td>C</td>\n", "      <td>A flaw was found in libtpms. The flaw can be t...</td>\n", "      <td>6.2</td>\n", "      <td>...</td>\n", "      <td>https://github.com/stefanberger/libtpms/raw/2e...</td>\n", "      <td>files/2022_3/1004</td>\n", "      <td>src/tpm2/Unmarshal.c</td>\n", "      <td>c</td>\n", "      <td>-1</td>\n", "      <td>{'flawfinder': [False, []], 'rats': [False, []...</td>\n", "      <td>{'src.tpm2.Unmarshal.TPM_KEY_BITS_Unmarshal': ...</td>\n", "      <td>{'src.tpm2.Unmarshal.TPMS_RSA_PARMS_Unmarshal'...</td>\n", "      <td>{'src.tpm2.Unmarshal.TPM_KEY_BITS_Unmarshal': ...</td>\n", "      <td>{}</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>37 rows × 39 columns</p>\n", "</div>"], "text/plain": ["                                     function_id  \\\n", "0   9a5467bf7b6e9e02ec9c3da4e23747c05faeaac6_118   \n", "1    d919b2744cd05abae043490f0a3dd1946c1ccb8c_20   \n", "2   fd6330292501983ac75fe4162275794b18445bd9_117   \n", "3    2b3dedfb9ba13f15887f22b935d373f36c9a59fa_57   \n", "4    00383e162ae2f8fc951f5745bf1011771acb8dce_16   \n", "5    c6247a9ef2c7b33244ab1d3aa5d629ec49f0a067_41   \n", "6    ebad9ddc4efb2635f37174c9d800d06206f1edf9_63   \n", "7   048a91e2682c1a8936ae34dbc7bd70291ec05410_157   \n", "8    97b153237c256c586e528eac7fc2f51aedb2b2fc_14   \n", "9    2e6173c273ca14adb11386db4e47622552b1c00e_74   \n", "10   d919b2744cd05abae043490f0a3dd1946c1ccb8c_21   \n", "11   b51b33f2bc5d1497ddf5bd107f791c101695000d_16   \n", "12    00383e162ae2f8fc951f5745bf1011771acb8dce_4   \n", "13    afd7086090dafd3949afd172822cbcec4ed17d56_6   \n", "14   afd7086090dafd3949afd172822cbcec4ed17d56_60   \n", "15   2b3dedfb9ba13f15887f22b935d373f36c9a59fa_24   \n", "16    afd7086090dafd3949afd172822cbcec4ed17d56_1   \n", "17   fd6330292501983ac75fe4162275794b18445bd9_80   \n", "18   97b153237c256c586e528eac7fc2f51aedb2b2fc_13   \n", "19   7ef09e1fc9ba07653dd078edb2408631c7969162_13   \n", "20    afd7086090dafd3949afd172822cbcec4ed17d56_5   \n", "21  bcac8e7acb514429fee9e0b5d0c7a0308fd4d76b_121   \n", "22  9a5467bf7b6e9e02ec9c3da4e23747c05faeaac6_119   \n", "23  e7e5cc9f68e895f59b317d4d1dac37ee15418094_145   \n", "24   da5c0f119203ad9728920456a0f52a6d850c01cd_12   \n", "25   97b153237c256c586e528eac7fc2f51aedb2b2fc_66   \n", "26   7ef09e1fc9ba07653dd078edb2408631c7969162_15   \n", "27   ebad9ddc4efb2635f37174c9d800d06206f1edf9_66   \n", "28   c6247a9ef2c7b33244ab1d3aa5d629ec49f0a067_43   \n", "29   afd7086090dafd3949afd172822cbcec4ed17d56_32   \n", "30   b51b33f2bc5d1497ddf5bd107f791c101695000d_37   \n", "31  bcac8e7acb514429fee9e0b5d0c7a0308fd4d76b_118   \n", "32  048a91e2682c1a8936ae34dbc7bd70291ec05410_159   \n", "33  e7e5cc9f68e895f59b317d4d1dac37ee15418094_100   \n", "34    da5c0f119203ad9728920456a0f52a6d850c01cd_3   \n", "35   fd6330292501983ac75fe4162275794b18445bd9_99   \n", "36  2e6173c273ca14adb11386db4e47622552b1c00e_123   \n", "\n", "                                             function  target  \\\n", "0   static int crypto_report_cipher(struct sk_buff...       1   \n", "1   static int on_part_data(\\n        multipart_pa...       1   \n", "2   float CLASS find_green (int bps, int bite, int...       1   \n", "3   void\\npci_lintr_release(struct pci_vdev *dev)\\...       1   \n", "4   opj_pi_iterator_t *opj_pi_initialise_encode(co...       1   \n", "5   static void\\nusage(const char *prog)\\n{\\n\\tfpr...       1   \n", "6   int options_parse(CONF_TYPE type) {\\n    SERVI...       1   \n", "7   bool SFD_GetFontMetaData( FILE *sfd,\\n\\t\\t\\t  ...       1   \n", "8   static int\\npci_emul_alloc_resource(uint64_t *...       1   \n", "9   TPM_RC\\nTPMT_TK_AUTH_Unmarshal(TPMT_TK_AUTH *t...       1   \n", "10  static int on_part_data_end(multipart_parser *...       1   \n", "11  OM_uint32 KRB5_CALLCONV\\nspnego_gss_init_sec_c...       1   \n", "12  static OPJ_BOOL opj_pi_next_pcrl(opj_pi_iterat...       1   \n", "13  static int writeBufferToSeparateTiles (TIFF* o...       1   \n", "14  static int\\ncreateImageSection(uint32_t sectsi...       1   \n", "15  int\\npci_emul_add_capability(struct pci_vdev *...       1   \n", "16  static int readContigTilesIntoBuffer (TIFF* in...       1   \n", "17  void CLASS samsung_load_raw()\\n{\\n  int row, c...       1   \n", "18  static int\\npci_emul_mem_handler(struct vmctx ...       1   \n", "19  static int\\nfind_sig8_target_as_global_offset(...       1   \n", "20  static int writeBufferToContigTiles (TIFF* out...       1   \n", "21  static ssize_t _hostsock_readv(\\n    oe_fd_t* ...       1   \n", "22  static int crypto_report_comp(struct sk_buff *...       1   \n", "23  void DebugAPI::slowPathTraceGeneratorFrame(JST...       1   \n", "24  int nfc_deactivate_target(struct nfc_dev *dev,...       1   \n", "25  static void\\npci_emul_cmdsts_write(struct pci_...       1   \n", "26  int\\ndwarf_global_formref_b(Dwarf_Attribute at...       1   \n", "27  void options_defaults() {\\n    SERVICE_OPTIONS...       1   \n", "28  int\\nkeepalived_main(int argc, char **argv)\\n{...       1   \n", "29  static int readContigStripsIntoBuffer (TIFF* i...       1   \n", "30  OM_uint32 KRB5_CALLCONV\\nspnego_gss_wrap_size_...       1   \n", "31  static int _hostsock_getsockname(\\n    oe_fd_t...       1   \n", "32  static SplineFont *SFD_GetFont( FILE *sfd,Spli...       1   \n", "33  bool DebugAPI::onSingleStep(JSContext* cx) {\\n...       1   \n", "34  int nfc_dev_down(struct nfc_dev *dev)\\n{\\n\\tin...       1   \n", "35  void CLASS parse_makernote (int base, int upta...       1   \n", "36  TPM_RC\\nTPMI_RSA_KEY_BITS_Unmarshal(TPMI_RSA_K...       1   \n", "\n", "                                    function_numbers  \\\n", "0         {'function_start': 74, 'function_end': 91}   \n", "1     {'function_start': 1861, 'function_end': 1910}   \n", "2   {'function_start': 13737, 'function_end': 13760}   \n", "3     {'function_start': 1776, 'function_end': 1793}   \n", "4     {'function_start': 1580, 'function_end': 1770}   \n", "5     {'function_start': 1074, 'function_end': 1172}   \n", "6       {'function_start': 377, 'function_end': 389}   \n", "7     {'function_start': 7628, 'function_end': 8453}   \n", "8       {'function_start': 470, 'function_end': 486}   \n", "9     {'function_start': 1749, 'function_end': 1770}   \n", "10    {'function_start': 1912, 'function_end': 1923}   \n", "11     {'function_start': 981, 'function_end': 1143}   \n", "12      {'function_start': 472, 'function_end': 601}   \n", "13    {'function_start': 1357, 'function_end': 1431}   \n", "14    {'function_start': 7546, 'function_end': 7591}   \n", "15      {'function_start': 762, 'function_end': 799}   \n", "16     {'function_start': 799, 'function_end': 1003}   \n", "17    {'function_start': 3569, 'function_end': 3601}   \n", "18      {'function_start': 420, 'function_end': 467}   \n", "19      {'function_start': 597, 'function_end': 624}   \n", "20    {'function_start': 1262, 'function_end': 1352}   \n", "21      {'function_start': 829, 'function_end': 866}   \n", "22       {'function_start': 93, 'function_end': 106}   \n", "23    {'function_start': 3809, 'function_end': 3879}   \n", "24      {'function_start': 442, 'function_end': 475}   \n", "25    {'function_start': 1979, 'function_end': 2043}   \n", "26      {'function_start': 675, 'function_end': 883}   \n", "27      {'function_start': 586, 'function_end': 597}   \n", "28    {'function_start': 1552, 'function_end': 1895}   \n", "29    {'function_start': 3825, 'function_end': 3856}   \n", "30    {'function_start': 2220, 'function_end': 2237}   \n", "31      {'function_start': 787, 'function_end': 817}   \n", "32    {'function_start': 8462, 'function_end': 9019}   \n", "33    {'function_start': 2599, 'function_end': 2724}   \n", "34      {'function_start': 137, 'function_end': 168}   \n", "35   {'function_start': 8261, 'function_end': 10105}   \n", "36    {'function_start': 3477, 'function_end': 3496}   \n", "\n", "                                         line_numbers  \\\n", "0   [{'line_change': '@@  -75,7 +75,7  @@ static i...   \n", "1   [{'line_change': '@@  -1867,7 +1865,7  @@ stat...   \n", "2   [{'line_change': '@@  -13739,7 +13794,8  @@ fl...   \n", "3   [{'line_change': '@@  -1781,7 +1800,10  @@ pci...   \n", "4   [{'line_change': '@@  -1580,7 +1594,8  @@ OPJ_...   \n", "5   [{'line_change': '@@  -1090,6 +1117,7  @@ usag...   \n", "6   [{'line_change': '@@  -379,11 +379,14  @@ int ...   \n", "7   [{'line_change': '@@  -7992,10 +7995,12  @@ bo...   \n", "8   [{'line_change': '@@  -473,7 +472,10  @@ pci_e...   \n", "9   [{'line_change': '@@  -1750,6 +1846,7  @@ TPM_...   \n", "10  [{'line_change': '@@  -1917,7 +1915,9  @@ stat...   \n", "11  [{'line_change': '@@  -1108,16 +1107,11  @@ sp...   \n", "12  [{'line_change': '@@  -475,6 +471,13  @@ stati...   \n", "13  [{'line_change': '@@  -1358,7 +1370,8  @@ stat...   \n", "14  [{'line_change': '@@  -7554,23 +7578,23  @@ cr...   \n", "15  [{'line_change': '@@  -765,8 +770,6  @@ pci_em...   \n", "16  [{'line_change': '@@  -810,8 +814,8  @@ static...   \n", "17  [{'line_change': '@@  -3569,6 +3582,11  @@ voi...   \n", "18  [{'line_change': '@@  -426,11 +426,10  @@ pci_...   \n", "19  [{'line_change': '@@  -605,7 +605,6  @@ find_s...   \n", "20  [{'line_change': '@@  -1267,7 +1277,7  @@ stat...   \n", "21  [{'line_change': '@@  -835,21 +1068,43  @@ sta...   \n", "22  [{'line_change': '@@  -94,8 +94,7  @@ static i...   \n", "23  [{'line_change': '@@  -3863,7 +3860,9  @@ void...   \n", "24  [{'line_change': '@@  -448,7 +448,7  @@ int nf...   \n", "25  [{'line_change': '@@  -2031,7 +2060,8  @@ pci_...   \n", "26  [{'line_change': '@@  -837,6 +836,13  @@ dwarf...   \n", "27  [{'line_change': '@@  -591,7 +589,7  @@ void o...   \n", "28  [{'line_change': '@@  -1582,6 +1617,9  @@ keep...   \n", "29  [{'line_change': '@@  -3825,10 +3841,10  @@ ex...   \n", "30  [{'line_change': '@@  -2227,8 +2275,13  @@ spn...   \n", "31  [{'line_change': '@@  -792,25 +1011,39  @@ sta...   \n", "32  [{'line_change': '@@  -8948,6 +8953,10  @@ exi...   \n", "33  [{'line_change': '@@  -2624,7 +2629,8  @@ bool...   \n", "34  [{'line_change': '@@  -142,7 +142,7  @@ int nf...   \n", "35  [{'line_change': '@@  -9890,37 +9914,68  @@ vo...   \n", "36  [{'line_change': '@@  -3478,6 +3596,7  @@ TPM_...   \n", "\n", "                                   commit_id          cve_id cve_language  \\\n", "0   9a5467bf7b6e9e02ec9c3da4e23747c05faeaac6   CVE-2013-2546            C   \n", "1   d919b2744cd05abae043490f0a3dd1946c1ccb8c  CVE-2021-44109            C   \n", "2   fd6330292501983ac75fe4162275794b18445bd9   CVE-2018-5810          C++   \n", "3   2b3dedfb9ba13f15887f22b935d373f36c9a59fa  CVE-2019-18844            C   \n", "4   00383e162ae2f8fc951f5745bf1011771acb8dce  CVE-2020-27841            C   \n", "5   c6247a9ef2c7b33244ab1d3aa5d629ec49f0a067  CVE-2018-19045            C   \n", "6   ebad9ddc4efb2635f37174c9d800d06206f1edf9  CVE-2021-20230            C   \n", "7   048a91e2682c1a8936ae34dbc7bd70291ec05410  CVE-2020-25690            C   \n", "8   97b153237c256c586e528eac7fc2f51aedb2b2fc  CVE-2019-18844            C   \n", "9   2e6173c273ca14adb11386db4e47622552b1c00e   CVE-2021-3623            C   \n", "10  d919b2744cd05abae043490f0a3dd1946c1ccb8c  CVE-2021-44108            C   \n", "11  b51b33f2bc5d1497ddf5bd107f791c101695000d   CVE-2015-2695            C   \n", "12  00383e162ae2f8fc951f5745bf1011771acb8dce  CVE-2020-27841            C   \n", "13  afd7086090dafd3949afd172822cbcec4ed17d56  CVE-2023-30775            C   \n", "14  afd7086090dafd3949afd172822cbcec4ed17d56  CVE-2023-30775            C   \n", "15  2b3dedfb9ba13f15887f22b935d373f36c9a59fa  CVE-2019-18844            C   \n", "16  afd7086090dafd3949afd172822cbcec4ed17d56  CVE-2023-30775            C   \n", "17  fd6330292501983ac75fe4162275794b18445bd9   CVE-2018-5810          C++   \n", "18  97b153237c256c586e528eac7fc2f51aedb2b2fc  CVE-2019-18844            C   \n", "19  7ef09e1fc9ba07653dd078edb2408631c7969162  CVE-2022-34299            C   \n", "20  afd7086090dafd3949afd172822cbcec4ed17d56  CVE-2023-30775            C   \n", "21  bcac8e7acb514429fee9e0b5d0c7a0308fd4d76b  CVE-2020-15224            C   \n", "22  9a5467bf7b6e9e02ec9c3da4e23747c05faeaac6   CVE-2013-2546            C   \n", "23  e7e5cc9f68e895f59b317d4d1dac37ee15418094  CVE-2023-29543          C++   \n", "24  da5c0f119203ad9728920456a0f52a6d850c01cd   CVE-2022-1974            C   \n", "25  97b153237c256c586e528eac7fc2f51aedb2b2fc  CVE-2019-18844            C   \n", "26  7ef09e1fc9ba07653dd078edb2408631c7969162  CVE-2022-34299            C   \n", "27  ebad9ddc4efb2635f37174c9d800d06206f1edf9  CVE-2021-20230            C   \n", "28  c6247a9ef2c7b33244ab1d3aa5d629ec49f0a067  CVE-2018-19045            C   \n", "29  afd7086090dafd3949afd172822cbcec4ed17d56  CVE-2023-30775            C   \n", "30  b51b33f2bc5d1497ddf5bd107f791c101695000d   CVE-2015-2695            C   \n", "31  bcac8e7acb514429fee9e0b5d0c7a0308fd4d76b  CVE-2020-15224            C   \n", "32  048a91e2682c1a8936ae34dbc7bd70291ec05410  CVE-2020-25690            C   \n", "33  e7e5cc9f68e895f59b317d4d1dac37ee15418094  CVE-2023-29543          C++   \n", "34  da5c0f119203ad9728920456a0f52a6d850c01cd   CVE-2022-1974            C   \n", "35  fd6330292501983ac75fe4162275794b18445bd9   CVE-2018-5812          C++   \n", "36  2e6173c273ca14adb11386db4e47622552b1c00e   CVE-2021-3623            C   \n", "\n", "                                      cve_description  cvss  ...  \\\n", "0   The report API in the crypto user configuratio...   9.8  ...   \n", "1   A buffer overflow in lib/sbi/message.c in Open...   7.5  ...   \n", "2   An error within the \"rollei_load_raw()\" functi...   8.8  ...   \n", "3   The Device Model in ACRN before 2019w25.5-1400...   7.5  ...   \n", "4   There's a flaw in openjpeg in versions prior t...   5.5  ...   \n", "5   keepalived 2.0.8 used mode 0666 when creating ...   7.5  ...   \n", "6   A flaw was found in stunnel before 5.57, where...   7.5  ...   \n", "7   An out-of-bounds write flaw was found in FontF...   8.8  ...   \n", "8   The Device Model in ACRN before 2019w25.5-1400...   7.5  ...   \n", "9   A flaw was found in libtpms. The flaw can be t...   6.2  ...   \n", "10  A null pointer dereference in src/amf/namf-han...   7.5  ...   \n", "11  lib/gssapi/spnego/spnego_mech.c in MIT Kerbero...   5.3  ...   \n", "12  There's a flaw in openjpeg in versions prior t...   5.5  ...   \n", "13  A vulnerability was found in the libtiff libra...   5.5  ...   \n", "14  A vulnerability was found in the libtiff libra...   5.5  ...   \n", "15  The Device Model in ACRN before 2019w25.5-1400...   7.5  ...   \n", "16  A vulnerability was found in the libtiff libra...   5.5  ...   \n", "17  An error within the \"rollei_load_raw()\" functi...   8.8  ...   \n", "18  The Device Model in ACRN before 2019w25.5-1400...   7.5  ...   \n", "19  There is a heap-based buffer over-read in libd...   8.1  ...   \n", "20  A vulnerability was found in the libtiff libra...   5.5  ...   \n", "21  In Open Enclave before version 0.12.0, an info...   6.8  ...   \n", "22  The report API in the crypto user configuratio...   9.8  ...   \n", "23  An attacker could have caused memory corruptio...   8.8  ...   \n", "24  A use-after-free flaw was found in the Linux k...   4.1  ...   \n", "25  The Device Model in ACRN before 2019w25.5-1400...   7.5  ...   \n", "26  There is a heap-based buffer over-read in libd...   8.1  ...   \n", "27  A flaw was found in stunnel before 5.57, where...   7.5  ...   \n", "28  keepalived 2.0.8 used mode 0666 when creating ...   7.5  ...   \n", "29  A vulnerability was found in the libtiff libra...   5.5  ...   \n", "30  lib/gssapi/spnego/spnego_mech.c in MIT Kerbero...   5.3  ...   \n", "31  In Open Enclave before version 0.12.0, an info...   6.8  ...   \n", "32  An out-of-bounds write flaw was found in FontF...   8.8  ...   \n", "33  An attacker could have caused memory corruptio...   8.8  ...   \n", "34  A use-after-free flaw was found in the Linux k...   4.1  ...   \n", "35  An error within the \"nikon_coolscan_load_raw()...   6.5  ...   \n", "36  A flaw was found in libtpms. The flaw can be t...   6.2  ...   \n", "\n", "                                                  url          file_path  \\\n", "0   https://github.com/torvalds/linux/raw/9a5467bf...    files/2013_3/34   \n", "1   https://github.com/open5gs/open5gs/raw/d919b27...   files/2022_4/715   \n", "2   https://github.com/LibRaw/LibRaw/raw/fd6330292...  files/2018_12\\216   \n", "3   https://github.com/projectacrn/acrn-hypervisor...  files/2019_11\\369   \n", "4   https://github.com/uclouvain/openjpeg/raw/0038...   files/2021_1/414   \n", "5   https://github.com/acassen/keepalived/raw/c624...   files/2018_11\\36   \n", "6   https://github.com/mtrojnar/stunnel/raw/ebad9d...   files/2021_2/109   \n", "7   https://github.com/fontforge/fontforge/raw/048...   files/2021_2/162   \n", "8   https://github.com/shuox/acrn-hypervisor/raw/9...  files/2019_11\\372   \n", "9   https://github.com/stefanberger/libtpms/raw/2e...  files/2022_3/1004   \n", "10  https://github.com/open5gs/open5gs/raw/d919b27...   files/2022_4/717   \n", "11  https://github.com/krb5/krb5/raw/b51b33f2bc5d1...   files/2015_11/16   \n", "12  https://github.com/uclouvain/openjpeg/raw/0038...   files/2021_1/414   \n", "13  https://github.com/libsdl-org/libtiff/raw/afd7...   files/2023_5/521   \n", "14  https://github.com/libsdl-org/libtiff/raw/afd7...   files/2023_5/521   \n", "15  https://github.com/projectacrn/acrn-hypervisor...  files/2019_11\\369   \n", "16  https://github.com/libsdl-org/libtiff/raw/afd7...   files/2023_5/521   \n", "17  https://github.com/LibRaw/LibRaw/raw/fd6330292...  files/2018_12\\216   \n", "18  https://github.com/shuox/acrn-hypervisor/raw/9...  files/2019_11\\372   \n", "19  https://github.com/davea42/libdwarf-code/raw/7...   files/2022_6/338   \n", "20  https://github.com/libsdl-org/libtiff/raw/afd7...   files/2023_5/521   \n", "21  https://github.com/openenclave/openenclave/raw...  files/2020_10/178   \n", "22  https://github.com/torvalds/linux/raw/9a5467bf...    files/2013_3/34   \n", "23  https://github.com/mozilla/gecko-dev/raw/e7e5c...   files/2023_6/909   \n", "24  https://github.com/torvalds/linux/raw/da5c0f11...     files/2022_8/9   \n", "25  https://github.com/shuox/acrn-hypervisor/raw/9...  files/2019_11\\372   \n", "26  https://github.com/davea42/libdwarf-code/raw/7...   files/2022_6/338   \n", "27  https://github.com/mtrojnar/stunnel/raw/ebad9d...   files/2021_2/109   \n", "28  https://github.com/acassen/keepalived/raw/c624...   files/2018_11\\36   \n", "29  https://github.com/libsdl-org/libtiff/raw/afd7...   files/2023_5/521   \n", "30  https://github.com/krb5/krb5/raw/b51b33f2bc5d1...   files/2015_11/16   \n", "31  https://github.com/openenclave/openenclave/raw...  files/2020_10/178   \n", "32  https://github.com/fontforge/fontforge/raw/048...   files/2021_2/162   \n", "33  https://github.com/mozilla/gecko-dev/raw/e7e5c...   files/2023_6/909   \n", "34  https://github.com/torvalds/linux/raw/da5c0f11...     files/2022_8/9   \n", "35  https://github.com/LibRaw/LibRaw/raw/fd6330292...  files/2018_12\\222   \n", "36  https://github.com/stefanberger/libtpms/raw/2e...  files/2022_3/1004   \n", "\n", "                              file_name file_language file_target  \\\n", "0                  crypto/crypto_user.c             c           1   \n", "1                     lib/sbi/message.c             c          -1   \n", "2             internal/dcraw_common.cpp           cpp          -1   \n", "3             devicemodel/hw/pci/core.c             c          -1   \n", "4                  src/lib/openjp2/pi.c             c          -1   \n", "5                keepalived/core/main.c             c           1   \n", "6                         src/options.c             c          -1   \n", "7                       fontforge/sfd.c             c           1   \n", "8             devicemodel/hw/pci/core.c             c          -1   \n", "9                  src/tpm2/Unmarshal.c             c          -1   \n", "10                    lib/sbi/message.c             c          -1   \n", "11  src/lib/gssapi/spnego/spnego_mech.c             c          -1   \n", "12                 src/lib/openjp2/pi.c             c          -1   \n", "13                     tools/tiffcrop.c             c           1   \n", "14                     tools/tiffcrop.c             c           1   \n", "15            devicemodel/hw/pci/core.c             c          -1   \n", "16                     tools/tiffcrop.c             c           1   \n", "17            internal/dcraw_common.cpp           cpp          -1   \n", "18            devicemodel/hw/pci/core.c             c          -1   \n", "19        src/lib/libdwarf/dwarf_form.c             c           1   \n", "20                     tools/tiffcrop.c             c           1   \n", "21  syscall/devices/hostsock/hostsock.c             c          -1   \n", "22                 crypto/crypto_user.c             c           1   \n", "23         js/src/debugger/Debugger.cpp           cpp          -1   \n", "24                       net/nfc/core.c             c          -1   \n", "25            devicemodel/hw/pci/core.c             c          -1   \n", "26        src/lib/libdwarf/dwarf_form.c             c           1   \n", "27                        src/options.c             c          -1   \n", "28               keepalived/core/main.c             c           1   \n", "29                     tools/tiffcrop.c             c           1   \n", "30  src/lib/gssapi/spnego/spnego_mech.c             c          -1   \n", "31  syscall/devices/hostsock/hostsock.c             c          -1   \n", "32                      fontforge/sfd.c             c           1   \n", "33         js/src/debugger/Debugger.cpp           cpp          -1   \n", "34                       net/nfc/core.c             c          -1   \n", "35            internal/dcraw_common.cpp           cpp          -1   \n", "36                 src/tpm2/Unmarshal.c             c          -1   \n", "\n", "                                               static  \\\n", "0   {'flawfinder': [True, ['/data/rdhu/other/Stati...   \n", "1   {'flawfinder': [False, []], 'rats': [False, []...   \n", "2   {'flawfinder': [True, ['/data/rdhu/other/Stati...   \n", "3   {'flawfinder': [<PERSON>alse, []], 'rats': [<PERSON>als<PERSON>, []...   \n", "4   {'flawfinder': [<PERSON>alse, []], 'rats': [<PERSON>als<PERSON>, []...   \n", "5   {'flawfinder': [True, ['/data/rdhu/other/Stati...   \n", "6   {'flawfinder': [True, ['/data/rdhu/other/Stati...   \n", "7   {'flawfinder': [False, []], 'rats': [True, ['/...   \n", "8   {'flawfinder': [<PERSON>alse, []], 'rats': [<PERSON>alse, []...   \n", "9   {'flawfinder': [<PERSON>alse, []], 'rats': [<PERSON>als<PERSON>, []...   \n", "10  {'flawfinder': [<PERSON>alse, []], 'rats': [<PERSON>als<PERSON>, []...   \n", "11  {'flawfinder': [<PERSON>alse, []], 'rats': [<PERSON>als<PERSON>, []...   \n", "12  {'flawfinder': [<PERSON>alse, []], 'rats': [<PERSON>als<PERSON>, []...   \n", "13  {'flawfinder': [True, ['/data/rdhu/other/Stati...   \n", "14  {'flawfinder': [True, ['/data/rdhu/other/Stati...   \n", "15  {'flawfinder': [<PERSON>alse, []], 'rats': [<PERSON>als<PERSON>, []...   \n", "16  {'flawfinder': [True, ['/data/rdhu/other/Stati...   \n", "17  {'flawfinder': [True, ['/data/rdhu/other/Stati...   \n", "18  {'flawfinder': [<PERSON>alse, []], 'rats': [<PERSON>als<PERSON>, []...   \n", "19  {'flawfinder': [True, ['/data/rdhu/other/Stati...   \n", "20  {'flawfinder': [True, ['/data/rdhu/other/Stati...   \n", "21  {'flawfinder': [<PERSON>alse, []], 'rats': [<PERSON>als<PERSON>, []...   \n", "22  {'flawfinder': [True, ['/data/rdhu/other/Stati...   \n", "23  {'flawfinder': [<PERSON>als<PERSON>, []], 'rats': [<PERSON>als<PERSON>, []...   \n", "24  {'flawfinder': [<PERSON>alse, []], 'rats': [<PERSON>als<PERSON>, []...   \n", "25  {'flawfinder': [<PERSON>alse, []], 'rats': [<PERSON>als<PERSON>, []...   \n", "26  {'flawfinder': [True, ['/data/rdhu/other/Stati...   \n", "27  {'flawfinder': [True, ['/data/rdhu/other/Stati...   \n", "28  {'flawfinder': [True, ['/data/rdhu/other/Stati...   \n", "29  {'flawfinder': [True, ['/data/rdhu/other/Stati...   \n", "30  {'flawfinder': [<PERSON>alse, []], 'rats': [<PERSON>als<PERSON>, []...   \n", "31  {'flawfinder': [<PERSON>als<PERSON>, []], 'rats': [<PERSON>als<PERSON>, []...   \n", "32  {'flawfinder': [False, []], 'rats': [True, ['/...   \n", "33  {'flawfinder': [<PERSON>als<PERSON>, []], 'rats': [<PERSON>als<PERSON>, []...   \n", "34  {'flawfinder': [<PERSON>als<PERSON>, []], 'rats': [<PERSON>als<PERSON>, []...   \n", "35  {'flawfinder': [True, ['/data/rdhu/other/Stati...   \n", "36  {'flawfinder': [<PERSON>als<PERSON>, []], 'rats': [<PERSON>als<PERSON>, []...   \n", "\n", "                                               caller  \\\n", "0                                                  {}   \n", "1                                                  {}   \n", "2                                                  {}   \n", "3                                                  {}   \n", "4                                                  {}   \n", "5                                                  {}   \n", "6   {'src.options.options_defaults': 'void options...   \n", "7   {'fontforge.sfd.SFD_GetFontMetaDataData_Init':...   \n", "8                                                  {}   \n", "9   {'src.tpm2.Unmarshal.TPM_ST_Unmarshal': 'TPM_R...   \n", "10                                                 {}   \n", "11  {'src.appl.gss-sample.gss-misc.send_token': 'i...   \n", "12                                                 {}   \n", "13                                                 {}   \n", "14                                                 {}   \n", "15                                                 {}   \n", "16                                                 {}   \n", "17                                                 {}   \n", "18                                                 {}   \n", "19                                                 {}   \n", "20                                                 {}   \n", "21                                                 {}   \n", "22                                                 {}   \n", "23                                                 {}   \n", "24                                                 {}   \n", "25                                                 {}   \n", "26  {'src.lib.libdwarf.dwarf_form.get_attr_dbg': '...   \n", "27                                                 {}   \n", "28                                                 {}   \n", "29                                                 {}   \n", "30  {'src.lib.gssapi.mechglue.g_seal.gss_wrap_size...   \n", "31                                                 {}   \n", "32                                                 {}   \n", "33                                                 {}   \n", "34                                                 {}   \n", "35                                                 {}   \n", "36  {'src.tpm2.Unmarshal.TPM_KEY_BITS_Unmarshal': ...   \n", "\n", "                                               callee  \\\n", "0                                                  {}   \n", "1                                                  {}   \n", "2                                                  {}   \n", "3   {'devicemodel.hw.pci.wdt_i6300esb.pci_wdt_dein...   \n", "4                                                  {}   \n", "5                                                  {}   \n", "6   {'src.options.options_cmdline': 'int options_c...   \n", "7   {'fontforge.sfd.SFD_GetFontMetaDataVoid': 'voi...   \n", "8   {'devicemodel.hw.pci.core.pci_emul_alloc_pbar'...   \n", "9                                                  {}   \n", "10                                                 {}   \n", "11                                                 {}   \n", "12  {'src.lib.openjp2.pi.opj_pi_next': 'OPJ_BOOL o...   \n", "13  {'tools.tiffcrop.writeSingleSection': 'static ...   \n", "14  {'tools.tiffcrop.writeImageSections': 'static ...   \n", "15  {'devicemodel.hw.pci.virtio.virtio.virtio_set_...   \n", "16  {'tools.tiffcrop.loadImage': 'static int\n", "loadI...   \n", "17                                                 {}   \n", "18  {'devicemodel.hw.pci.core.modify_bar_registrat...   \n", "19  {'src.lib.libdwarf.dwarf_form.dwarf_global_for...   \n", "20  {'tools.tiffcrop.writeSingleSection': 'static ...   \n", "21                                                 {}   \n", "22                                                 {}   \n", "23                                                 {}   \n", "24                                                 {}   \n", "25  {'devicemodel.hw.pci.core.pci_cfgrw': 'static ...   \n", "26  {'src.lib.libdwarf.dwarf_form.dwarf_global_for...   \n", "27  {'src.options.options_parse': 'int options_par...   \n", "28                                                 {}   \n", "29  {'tools.tiffcrop.loadImage': 'static int\n", "loadI...   \n", "30                                                 {}   \n", "31                                                 {}   \n", "32                                                 {}   \n", "33                                                 {}   \n", "34                                                 {}   \n", "35                                                 {}   \n", "36  {'src.tpm2.Unmarshal.TPMS_RSA_PARMS_Unmarshal'...   \n", "\n", "                                     caller_of_change  \\\n", "0                                                  {}   \n", "1                                                  {}   \n", "2                                                  {}   \n", "3                                                  {}   \n", "4                                                  {}   \n", "5                                                  {}   \n", "6   {'src.options.options_defaults': 'void options...   \n", "7   {'Unicode.char.strmatch': 'int strmatch(const ...   \n", "8                                                  {}   \n", "9   {'src.tpm2.Unmarshal.TPM_ST_Unmarshal': 'TPM_R...   \n", "10                                                 {}   \n", "11  {'src.lib.gssapi.mechglue.g_rel_buffer.gss_rel...   \n", "12                                                 {}   \n", "13                                                 {}   \n", "14                                                 {}   \n", "15                                                 {}   \n", "16                                                 {}   \n", "17                                                 {}   \n", "18                                                 {}   \n", "19                                                 {}   \n", "20                                                 {}   \n", "21                                                 {}   \n", "22                                                 {}   \n", "23                                                 {}   \n", "24                                                 {}   \n", "25                                                 {}   \n", "26  {'src.lib.libdwarf.dwarf_form.find_sig8_target...   \n", "27                                                 {}   \n", "28                                                 {}   \n", "29                                                 {}   \n", "30  {'src.lib.gssapi.mechglue.g_seal.gss_wrap_size...   \n", "31                                                 {}   \n", "32                                                 {}   \n", "33                                                 {}   \n", "34                                                 {}   \n", "35                                                 {}   \n", "36  {'src.tpm2.Unmarshal.TPM_KEY_BITS_Unmarshal': ...   \n", "\n", "                                     callee_of_change  \n", "0                                                  {}  \n", "1                                                  {}  \n", "2                                                  {}  \n", "3   {'devicemodel.hw.pci.core.pci_emul_deinit': 's...  \n", "4                                                  {}  \n", "5                                                  {}  \n", "6   {'src.options.options_cmdline': 'int options_c...  \n", "7   {'fontforge.sfd.SFD_GetFontMetaData': 'bool SF...  \n", "8   {'devicemodel.hw.pci.core.pci_emul_alloc_pbar'...  \n", "9                                                  {}  \n", "10                                                 {}  \n", "11                                                 {}  \n", "12                                                 {}  \n", "13  {'tools.tiffcrop.writeSingleSection': 'static ...  \n", "14                                                 {}  \n", "15  {'devicemodel.hw.pci.core.pci_emul_add_msicap'...  \n", "16  {'tools.tiffcrop.loadImage': 'static int\n", "loadI...  \n", "17                                                 {}  \n", "18  {'devicemodel.hw.pci.core.modify_bar_registrat...  \n", "19  {'src.lib.libdwarf.dwarf_form.dwarf_global_for...  \n", "20  {'tools.tiffcrop.writeSingleSection': 'static ...  \n", "21                                                 {}  \n", "22                                                 {}  \n", "23                                                 {}  \n", "24                                                 {}  \n", "25  {'devicemodel.hw.pci.core.pci_cfgrw': 'static ...  \n", "26                                                 {}  \n", "27  {'src.options.options_parse': 'int options_par...  \n", "28                                                 {}  \n", "29  {'tools.tiffcrop.loadImage': 'static int\n", "loadI...  \n", "30                                                 {}  \n", "31                                                 {}  \n", "32                                                 {}  \n", "33                                                 {}  \n", "34                                                 {}  \n", "35                                                 {}  \n", "36                                                 {}  \n", "\n", "[37 rows x 39 columns]"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["# Get commit_ids with more than one row in cleaned_df\n", "multi_row_commit_ids = commit_counts[commit_counts > 1].index\n", "\n", "# Filter cleaned_df to only those rows\n", "multi_row_cleaned_df = cleaned_df[cleaned_df['commit_id'].isin(multi_row_commit_ids)].copy().reset_index(drop=True)\n", "multi_row_cleaned_df"]}, {"cell_type": "code", "execution_count": 22, "id": "4faa372b", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import math\n", "import ast\n", "\n", "def _parse_hunk_lines(hunk_text: str):\n", "    lines = []\n", "    for raw in hunk_text.splitlines():\n", "        if raw.startswith('@@'):\n", "            continue  # header\n", "        if raw and raw[0] in (' ', '+', '-', '@'):\n", "            tag, text = raw[0], raw[1:]\n", "        else:\n", "            tag, text = ' ', raw  # assume context if no prefix\n", "        lines.append((tag, text))\n", "    return lines\n", "\n", "def _find_subsequence(haystack, needle):\n", "    if not needle:\n", "        return None\n", "    n = len(needle)\n", "    for i in range(len(haystack) - n + 1):\n", "        ok = True\n", "        for j, s in enumerate(needle):\n", "            if haystack[i + j].rstrip() != s.rstrip():\n", "                ok = False\n", "                break\n", "        if ok:\n", "            return i\n", "    return None\n", "\n", "def _apply_single_hunk(func_text: str, hunk_text: str) -> str:\n", "    func_lines = func_text.splitlines()\n", "    hunk = _parse_hunk_lines(hunk_text)\n", "\n", "    src_seq = [t for tag, t in hunk if tag in (' ', '-')]\n", "    start = _find_subsequence(func_lines, src_seq)\n", "\n", "    if start is None:\n", "        ctx_seq = [t for tag, t in hunk if tag == ' ']\n", "        start = _find_subsequence(func_lines, ctx_seq)\n", "\n", "    if start is None:\n", "        return func_text\n", "\n", "    i = start\n", "    new_segment = []\n", "    for tag, text in hunk:\n", "        if tag == ' ':\n", "            new_segment.append(func_lines[i])\n", "            i += 1\n", "        elif tag == '-':\n", "            i += 1\n", "        elif tag == '+':\n", "            new_segment.append(text)\n", "\n", "    consumed = len(src_seq)\n", "    func_lines = func_lines[:start] + new_segment + func_lines[start + consumed:]\n", "    return \"\\n\".join(func_lines)\n", "\n", "def _coerce_line_numbers(val):\n", "    \"\"\"\n", "    Normalize `line_numbers` into a list of hunk strings.\n", "    Accepts: NaN/None, dict, list/tuple, numpy array, pandas Series, or string\n", "    (possibly a stringified list of dicts).\n", "    \"\"\"\n", "    # None/NaN\n", "    if val is None or (isinstance(val, float) and math.isnan(val)):\n", "        return []\n", "\n", "    # pandas Series -> list\n", "    if isinstance(val, pd.Series):\n", "        val = val.tolist()\n", "\n", "    # numpy array -> list (optional import)\n", "    try:\n", "        import numpy as np\n", "        if isinstance(val, np.ndarray):\n", "            val = val.tolist()\n", "    except Exception:\n", "        pass\n", "\n", "    # dict -> wrap\n", "    if isinstance(val, dict):\n", "        val = [val]\n", "\n", "    # string -> either parse list/dicts or single hunk string\n", "    if isinstance(val, str):\n", "        s = val.strip()\n", "        if (s.startswith('[') and s.endswith(']')) or (s.startswith('{') and s.endswith('}')):\n", "            try:\n", "                parsed = ast.literal_eval(s)\n", "                val = parsed if isinstance(parsed, (list, tuple)) else [parsed]\n", "            except Exception:\n", "                val = [s]\n", "        else:\n", "            val = [s]\n", "\n", "    # ensure iterable list/tuple\n", "    if not isinstance(val, (list, tuple)):\n", "        val = [val]\n", "\n", "    # Extract actual hunk strings\n", "    hunks = []\n", "    for item in val:\n", "        if not item:\n", "            continue\n", "        if isinstance(item, dict):\n", "            h = item.get('line_change')\n", "            if h:\n", "                hunks.append(h)\n", "        else:\n", "            hunks.append(str(item))\n", "    return hunks\n", "\n", "def _apply_all_hunks_to_function(func_text, line_numbers):\n", "    hunks = _coerce_line_numbers(line_numbers)\n", "    if not hunks:\n", "        return func_text\n", "    out = func_text\n", "    for hunk_text in hunks:\n", "        out = _apply_single_hunk(out, hunk_text)\n", "    return out\n", "\n", "# ---- Run over your DataFrame ----\n", "multi_row_cleaned_df['function_after'] = multi_row_cleaned_df.apply(\n", "    lambda row: _apply_all_hunks_to_function(row['function'], row['line_numbers']),\n", "    axis=1\n", ")\n"]}, {"cell_type": "code", "execution_count": 23, "id": "27a3f0d9", "metadata": {}, "outputs": [], "source": ["# test_function_after.py\n", "import re\n", "import pytest\n", "\n", "def _normalize_line(s: str) -> str:\n", "    return re.sub(r\"\\s+\", \" \", s.strip())\n", "\n", "def _normalized_lineset(text: str) -> set[str]:\n", "    return {\n", "        _normalize_line(line)\n", "        for line in text.splitlines()\n", "        if _normalize_line(line)  # skip empty after normalization\n", "    }\n", "\n", "def _extract_added_lines_from_hunk(hunk_text: str) -> list[str]:\n", "    \"\"\"\n", "    Parse a unified-diff hunk text and return the list of added lines\n", "    (without the leading '+'), excluding headers and blank additions.\n", "    \"\"\"\n", "    added = []\n", "    for raw in hunk_text.splitlines():\n", "        if raw.startswith(\"@@\") or raw.startswith(\"+++\") or raw.startswith(\"---\"):\n", "            continue\n", "        if raw.startswith(\"+\"):\n", "            line = raw[1:]\n", "            if line.strip():\n", "                added.append(line)\n", "    return added\n", "\n", "def _collect_all_added_lines(line_numbers: list[dict]) -> list[str]:\n", "    added = []\n", "    for h in (line_numbers or []):\n", "        hunk = h.get(\"line_change\", \"\")\n", "        added.extend(_extract_added_lines_from_hunk(hunk))\n", "    return added\n", "\n", "def test_function_after_contains_all_added_lines(df_with_function_after):\n", "    \"\"\"\n", "    df_with_function_after must have columns:\n", "      ['function_id', 'line_numbers', 'function_after']\n", "    \"\"\"\n", "    for idx, row in df_with_function_after.iterrows():\n", "        fid = row.get(\"function_id\", f\"<row {idx}>\")\n", "        fa = row.get(\"function_after\", None)\n", "\n", "        # Sanity checks on construction outcome\n", "        if fa is None:\n", "            pytest.fail(f\"{fid}: missing 'function_after' value (None).\")\n", "\n", "        if isinstance(fa, BaseException):\n", "            # Surface the original error clearly\n", "            pytest.fail(f\"{fid}: row_to_function_after failed: \"\n", "                        f\"{fa.__class__.__name__}: {fa}\")\n", "\n", "        if not isinstance(fa, str):\n", "            pytest.fail(f\"{fid}: 'function_after' must be str, got {type(fa).__name__!s}: {fa!r}\")\n", "\n", "        added_lines = _collect_all_added_lines(row.get(\"line_numbers\") or [])\n", "\n", "        # If no lines were added by the diff for this row, the inclusion check is vacuously true.\n", "        if not added_lines:\n", "            continue\n", "\n", "        fn_lineset = _normalized_lineset(fa)\n", "        missing = [l for l in added_lines if _normalize_line(l) not in fn_lineset]\n", "\n", "        assert not missing, (\n", "            f\"{fid}: {len(missing)} added line(s) not found in function_after: {missing}\"\n", "        )\n", "\n", "\n", "test_function_after_contains_all_added_lines(multi_row_cleaned_df)"]}, {"cell_type": "code", "execution_count": 24, "id": "6246494c", "metadata": {}, "outputs": [{"data": {"text/plain": ["function_id              9a5467bf7b6e9e02ec9c3da4e23747c05faeaac6_118\n", "function            static int crypto_report_cipher(struct sk_buff...\n", "target                                                              1\n", "function_numbers           {'function_start': 74, 'function_end': 91}\n", "line_numbers        [{'line_change': '@@  -75,7 +75,7  @@ static i...\n", "commit_id                    9a5467bf7b6e9e02ec9c3da4e23747c05faeaac6\n", "cve_id                                                  CVE-2013-2546\n", "cve_language                                                        C\n", "cve_description     The report API in the crypto user configuratio...\n", "cvss                                                              9.8\n", "publish_date                                           March 14, 2013\n", "cwe_id                                                      [CWE-310]\n", "cwe_description                                                      \n", "cwe_consequence                                                      \n", "cwe_method                                                           \n", "cwe_solution                                                         \n", "AV                                                            NETWORK\n", "AC                                                            NETWORK\n", "PR                                                               NONE\n", "UI                                                               NONE\n", "S                                                           UNCHANGED\n", "C                                                                HIGH\n", "I                                                                HIGH\n", "A                                                                HIGH\n", "commit_message      crypto: user - fix info leaks in report API\\n\\...\n", "commit_date                                      2013-02-19T12:27:03Z\n", "parents             [{'commit_id_before': '7eb9c5df92361c55daab4d8...\n", "project                                                torvalds/linux\n", "outdated                                                            0\n", "url                 https://github.com/torvalds/linux/raw/9a5467bf...\n", "file_path                                             files/2013_3/34\n", "file_name                                        crypto/crypto_user.c\n", "file_language                                                       c\n", "file_target                                                         1\n", "static              {'flawfinder': [True, ['/data/rdhu/other/Stati...\n", "caller                                                             {}\n", "callee                                                             {}\n", "caller_of_change                                                   {}\n", "callee_of_change                                                   {}\n", "function_after      static int crypto_report_cipher(struct sk_buff...\n", "Name: 0, dtype: object"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["multi_row_cleaned_df.loc[0]"]}, {"cell_type": "code", "execution_count": 25, "id": "dce6e4ba", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["static int crypto_report_cipher(struct sk_buff *skb, struct crypto_alg *alg)\n", "{\n", "\tstruct crypto_report_cipher rcipher;\n", "\n", "\tsnprintf(rcipher.type, CRYPTO_MAX_ALG_NAME, \"%s\", \"cipher\");\n", "\n", "\trcipher.blocksize = alg->cra_blocksize;\n", "\trcipher.min_keysize = alg->cra_cipher.cia_min_keysize;\n", "\trcipher.max_keysize = alg->cra_cipher.cia_max_keysize;\n", "\n", "\tif (nla_put(skb, CRYPTOCFGA_REPORT_CIPHER,\n", "\t\t    sizeof(struct crypto_report_cipher), &rcipher))\n", "\t\tgoto nla_put_failure;\n", "\treturn 0;\n", "\n", "nla_put_failure:\n", "\treturn -EMSGSIZE;\n", "}\n"]}], "source": ["print(multi_row_cleaned_df.loc[0]['function'])"]}, {"cell_type": "code", "execution_count": 26, "id": "f1fb8c52", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["static int crypto_report_cipher(struct sk_buff *skb, struct crypto_alg *alg)\n", "{\n", "\tstruct crypto_report_cipher rcipher;\n", "\n", "\tstrncpy(rcipher.type, \"cipher\", sizeof(rcipher.type));\n", "\n", "\trcipher.blocksize = alg->cra_blocksize;\n", "\trcipher.min_keysize = alg->cra_cipher.cia_min_keysize;\n", "\trcipher.max_keysize = alg->cra_cipher.cia_max_keysize;\n", "\n", "\tif (nla_put(skb, CRYPTOCFGA_REPORT_CIPHER,\n", "\t\t    sizeof(struct crypto_report_cipher), &rcipher))\n", "\t\tgoto nla_put_failure;\n", "\treturn 0;\n", "\n", "nla_put_failure:\n", "\treturn -EMSGSIZE;\n", "}\n"]}], "source": ["print(multi_row_cleaned_df.loc[0]['function_after'])"]}, {"cell_type": "code", "execution_count": 27, "id": "ab97a35e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Numbers:\n", "@@  -75,7 +75,7  @@ static int crypto_report_cipher(struct sk_buff *skb, struct crypto_alg *alg)\n", " {\n", " \tstruct crypto_report_cipher rcipher;\n", " \n", "-\tsnprintf(rcipher.type, CRYPTO_MAX_ALG_NAME, \"%s\", \"cipher\");\n", "+\tstrncpy(rcipher.type, \"cipher\", sizeof(rcipher.type));\n", " \n", " \trcipher.blocksize = alg->cra_blocksize;\n", " \trcipher.min_keysize = alg->cra_cipher.cia_min_keysize;\n", "\n"]}], "source": ["print(\"Numbers:\")\n", "print(multi_row_cleaned_df.iloc[0][\"line_numbers\"][0]['line_change'])"]}, {"cell_type": "code", "execution_count": 50, "id": "a00dd029", "metadata": {}, "outputs": [{"data": {"text/plain": ["14"]}, "execution_count": 50, "metadata": {}, "output_type": "execute_result"}], "source": ["# Find commit_ids that appear with multiple different cve_ids\n", "commit_cve_counts = multi_row_cleaned_df.groupby('commit_id')['cve_id'].nunique()\n", "single_cve_commits = commit_cve_counts[commit_cve_counts == 1].index\n", "\n", "# Filter to keep only rows where commit_id has a single cve_id\n", "multi_row_cleaned_df = multi_row_cleaned_df[multi_row_cleaned_df['commit_id'].isin(single_cve_commits)].copy().reset_index(drop=True)\n", "multi_row_cleaned_df['commit_id'].nunique()"]}, {"cell_type": "code", "execution_count": 51, "id": "3cb4c5e5", "metadata": {}, "outputs": [{"data": {"text/plain": ["13"]}, "execution_count": 51, "metadata": {}, "output_type": "execute_result"}], "source": ["multi_row_cleaned_df['cve_id'].nunique()"]}, {"cell_type": "code", "execution_count": 52, "id": "23b87cb8", "metadata": {}, "outputs": [], "source": ["multi_row_cleaned_df.to_csv(\"mh_sf_reposvul.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "56617f26", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "cb_repair", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}
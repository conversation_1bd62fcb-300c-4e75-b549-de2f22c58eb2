from utils.repair_agent import RepairAgent
import ast
from utils.cve_database import CVEDatabase
from utils.cve_pipeline import CVEBenchPipeline
import json
import os
from tqdm import tqdm 
import signal
from dotenv import load_dotenv

class TimeoutException(Exception):
    pass

def handler(signum, frame):
    raise TimeoutException("Timeout: repair_cve took more than 10 minutes")

# Example usage
if __name__ == '__main__':
    load_dotenv()
    signal.signal(signal.SIGALRM, handler)

    cve_database = CVEDatabase()
    cve_id_map = json.load(open("cves/cve_list.json", "r"))
    cve_ids = cve_id_map["Java"] + cve_id_map["Javascript"] + cve_id_map["PHP"] + cve_id_map["Python"]

    skipped_count = 0
    progress_bar = tqdm(cve_ids, desc="Processing CVEs")

    for cve_id in progress_bar:
    # for cve_id in cve_id_map["Java"]+cve_id_map["Javascript"]+cve_id_map["PHP"]+cve_id_map["Python"]:
        cve_pipeline = CVEBenchPipeline(
            cve_database=cve_database,
            cve_id=cve_id
        )
        cve_info_dict = cve_pipeline.extract_info()
        if not cve_info_dict:
            skipped_count += 1
            progress_bar.set_postfix(skipped=skipped_count)
            continue

        print("CVE Description:", cve_info_dict['cve_desc'])
        # for info_level in ["blackbox", "midbox", "whitebox"]:
        try:
            signal.alarm(60)
            cve_pipeline.repair_cve(
                cve_info_dict=cve_info_dict, 
                model_choice='openai',
                model_name=os.getenv('GEMINI_MODEL'),
                client_api_key=os.getenv('GEMINI_API_KEY'),
                base_url=os.getenv('GEMINI_BASE_URL'),
                tools_allowed=['ls','cat','git','bandit'], 
                patch_folder="cves_patches", 
                patch_filename=cve_id
            )
        except TimeoutException as te:
            print("Timeout:", f"{cve_id} - {str(te)}")
            progress_bar.set_postfix(skipped=skipped_count)
        except Exception:
            print("Error processing:", f"{cve_id}")
            progress_bar.set_postfix(skipped=skipped_count)
